{"name": "@react-navigation/bottom-tabs", "description": "Bottom tab navigator following iOS design guidelines", "version": "7.4.2", "keywords": ["react-native-component", "react-component", "react-native", "react-navigation", "ios", "android", "tab"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/bottom-tabs"}, "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/elements": "^2.5.2", "color": "^4.2.3"}, "devDependencies": {"@jest/globals": "^30.0.0", "@react-navigation/native": "^7.1.14", "@testing-library/react-native": "^13.2.0", "@types/color": "^4.2.0", "@types/react": "~19.0.10", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-builder-bob": "^0.40.12", "react-native-safe-area-context": "5.5.0", "react-native-screens": "~4.11.1", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@react-navigation/native": "^7.1.14", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0", "react-native-screens": ">= 4.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "f2ca954afa5a478de49489199444d7a5269df261"}