{"version": 3, "names": ["appendToMemberExpression", "member", "append", "computed", "object", "memberExpression", "property"], "sources": ["../../src/modifications/appendToMemberExpression.ts"], "sourcesContent": ["import { memberExpression } from \"../builders/generated\";\nimport type * as t from \"..\";\n\n/**\n * Append a node to a member expression.\n */\nexport default function appendToMemberExpression(\n  member: t.MemberExpression,\n  append: t.MemberExpression[\"property\"],\n  computed: boolean = false,\n): t.MemberExpression {\n  member.object = memberExpression(\n    member.object,\n    member.property,\n    member.computed,\n  );\n  member.property = append;\n  member.computed = !!computed;\n\n  return member;\n}\n"], "mappings": ";;;;;;;AAAA;;AAMe,SAASA,wBAAT,CACbC,MADa,EAEbC,MAFa,EAGbC,QAAiB,GAAG,KAHP,EAIO;EACpBF,MAAM,CAACG,MAAP,GAAgB,IAAAC,2BAAA,EACdJ,MAAM,CAACG,MADO,EAEdH,MAAM,CAACK,QAFO,EAGdL,MAAM,CAACE,QAHO,CAAhB;EAKAF,MAAM,CAACK,QAAP,GAAkBJ,MAAlB;EACAD,MAAM,CAACE,QAAP,GAAkB,CAAC,CAACA,QAApB;EAEA,OAAOF,MAAP;AACD"}