{"version": 3, "names": ["buildChildren", "node", "elements", "i", "children", "length", "child", "isJSXText", "cleanJSXElementLiteralChild", "isJSXExpressionContainer", "expression", "isJSXEmptyExpression", "push"], "sources": ["../../../src/builders/react/buildChildren.ts"], "sourcesContent": ["import {\n  isJSXText,\n  isJSXExpressionContainer,\n  isJSXEmptyExpression,\n} from \"../../validators/generated\";\nimport cleanJSXElementLiteralChild from \"../../utils/react/cleanJSXElementLiteralChild\";\nimport type * as t from \"../..\";\n\ntype ReturnedChild =\n  | t.JSXSpreadChild\n  | t.JSXElement\n  | t.JSXFragment\n  | t.Expression;\n\nexport default function buildChildren(\n  node: t.JSXElement | t.JSXFragment,\n): ReturnedChild[] {\n  const elements = [];\n\n  for (let i = 0; i < node.children.length; i++) {\n    let child: any = node.children[i];\n\n    if (isJSXText(child)) {\n      cleanJSXElementLiteralChild(child, elements);\n      continue;\n    }\n\n    if (isJSXExpressionContainer(child)) child = child.expression;\n    if (isJSXEmptyExpression(child)) continue;\n\n    elements.push(child);\n  }\n\n  return elements;\n}\n"], "mappings": ";;;;;;;AAAA;;AAKA;;AASe,SAASA,aAAT,CACbC,IADa,EAEI;EACjB,MAAMC,QAAQ,GAAG,EAAjB;;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,IAAI,CAACG,QAAL,CAAcC,MAAlC,EAA0CF,CAAC,EAA3C,EAA+C;IAC7C,IAAIG,KAAU,GAAGL,IAAI,CAACG,QAAL,CAAcD,CAAd,CAAjB;;IAEA,IAAI,IAAAI,oBAAA,EAAUD,KAAV,CAAJ,EAAsB;MACpB,IAAAE,oCAAA,EAA4BF,KAA5B,EAAmCJ,QAAnC;MACA;IACD;;IAED,IAAI,IAAAO,mCAAA,EAAyBH,KAAzB,CAAJ,EAAqCA,KAAK,GAAGA,KAAK,CAACI,UAAd;IACrC,IAAI,IAAAC,+BAAA,EAAqBL,KAArB,CAAJ,EAAiC;IAEjCJ,QAAQ,CAACU,IAAT,CAAcN,KAAd;EACD;;EAED,OAAOJ,QAAP;AACD"}