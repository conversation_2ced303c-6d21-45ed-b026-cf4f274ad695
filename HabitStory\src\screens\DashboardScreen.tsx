import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';

import { useEntries } from '../hooks/useEntries';
import { useHabitTracking } from '../hooks/useHabitTracking';
import { formatDate } from '../lib/db';

interface DashboardStats {
  totalEntries: number;
  currentStreak: number;
  longestStreak: number;
  thisWeekEntries: number;
  topHabits: { habit_name: string; count: number; category: string }[];
}

const DashboardScreen: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalEntries: 0,
    currentStreak: 0,
    longestStreak: 0,
    thisWeekEntries: 0,
    topHabits: [],
  });
  const [todayEntry, setTodayEntry] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  const navigation = useNavigation();
  const { getAllEntries, getEntryByDate } = useEntries();
  const { todayHabits, allHabits } = useHabitTracking();

  const today = formatDate(new Date());

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setIsLoading(true);
    try {
      const [allEntries, todayEntryData] = await Promise.all([
        getAllEntries(),
        getEntryByDate(today),
      ]);

      // Calculate stats
      const totalEntries = allEntries.length;
      const currentStreak = calculateCurrentStreak(allEntries);
      const longestStreak = calculateLongestStreak(allEntries);
      const thisWeekEntries = calculateThisWeekEntries(allEntries);
      const topHabits = allHabits.slice(0, 5); // Top 5 habits

      setStats({
        totalEntries,
        currentStreak,
        longestStreak,
        thisWeekEntries,
        topHabits,
      });

      setTodayEntry(todayEntryData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateCurrentStreak = (entries: any[]): number => {
    if (entries.length === 0) return 0;

    const sortedEntries = entries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    let streak = 0;
    const today = new Date();

    for (let i = 0; i < sortedEntries.length; i++) {
      const entryDate = new Date(sortedEntries[i].date);
      const expectedDate = new Date(today);
      expectedDate.setDate(today.getDate() - i);

      if (entryDate.toDateString() === expectedDate.toDateString()) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  };

  const calculateLongestStreak = (entries: any[]): number => {
    if (entries.length === 0) return 0;

    const sortedEntries = entries.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    let longestStreak = 1;
    let currentStreak = 1;

    for (let i = 1; i < sortedEntries.length; i++) {
      const prevDate = new Date(sortedEntries[i - 1].date);
      const currentDate = new Date(sortedEntries[i].date);
      const diffTime = currentDate.getTime() - prevDate.getTime();
      const diffDays = diffTime / (1000 * 60 * 60 * 24);

      if (diffDays === 1) {
        currentStreak++;
        longestStreak = Math.max(longestStreak, currentStreak);
      } else {
        currentStreak = 1;
      }
    }

    return longestStreak;
  };

  const calculateThisWeekEntries = (entries: any[]): number => {
    const today = new Date();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - today.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    return entries.filter(entry => {
      const entryDate = new Date(entry.date);
      return entryDate >= startOfWeek;
    }).length;
  };

  const navigateToJournal = () => {
    navigation.navigate('Journal' as never);
  };

  const navigateToHistory = () => {
    navigation.navigate('History' as never);
  };

  const renderQuickStats = () => (
    <View className="bg-white rounded-lg p-4 mb-4 shadow-sm border border-gray-100">
      <Text className="text-lg font-semibold text-gray-800 mb-3">Quick Stats</Text>
      <View className="flex-row justify-between">
        <View className="items-center flex-1">
          <Text className="text-2xl font-bold text-blue-600">{stats.totalEntries}</Text>
          <Text className="text-sm text-gray-600">Total Entries</Text>
        </View>
        <View className="items-center flex-1">
          <Text className="text-2xl font-bold text-green-600">{stats.currentStreak}</Text>
          <Text className="text-sm text-gray-600">Current Streak</Text>
        </View>
        <View className="items-center flex-1">
          <Text className="text-2xl font-bold text-purple-600">{stats.longestStreak}</Text>
          <Text className="text-sm text-gray-600">Best Streak</Text>
        </View>
        <View className="items-center flex-1">
          <Text className="text-2xl font-bold text-orange-600">{stats.thisWeekEntries}</Text>
          <Text className="text-sm text-gray-600">This Week</Text>
        </View>
      </View>
    </View>
  );

  const renderTodayStatus = () => (
    <View className="bg-white rounded-lg p-4 mb-4 shadow-sm border border-gray-100">
      <Text className="text-lg font-semibold text-gray-800 mb-3">Today's Status</Text>
      {todayEntry ? (
        <View>
          <View className="flex-row items-center mb-2">
            <Text className="text-green-600 text-lg mr-2">✅</Text>
            <Text className="text-green-700 font-medium">Journal entry completed</Text>
          </View>
          <Text className="text-gray-600 text-sm mb-3">
            {todayEntry.text.length > 100 
              ? `${todayEntry.text.substring(0, 100)}...` 
              : todayEntry.text}
          </Text>
          <TouchableOpacity
            onPress={navigateToJournal}
            className="bg-blue-500 rounded-lg py-2 px-4 self-start"
          >
            <Text className="text-white font-medium">Edit Today's Entry</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View>
          <View className="flex-row items-center mb-2">
            <Text className="text-gray-400 text-lg mr-2">⭕</Text>
            <Text className="text-gray-600 font-medium">No journal entry yet</Text>
          </View>
          <Text className="text-gray-500 text-sm mb-3">
            Start your day by writing about your thoughts and experiences.
          </Text>
          <TouchableOpacity
            onPress={navigateToJournal}
            className="bg-green-500 rounded-lg py-3 px-6 self-start"
          >
            <Text className="text-white font-medium">Write Today's Entry</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderTodayHabits = () => (
    <View className="bg-white rounded-lg p-4 mb-4 shadow-sm border border-gray-100">
      <Text className="text-lg font-semibold text-gray-800 mb-3">Today's Habits</Text>
      {todayHabits.length > 0 ? (
        <View>
          {todayHabits.slice(0, 5).map((habit, index) => (
            <View key={index} className="flex-row items-center justify-between py-2">
              <View className="flex-row items-center flex-1">
                <Text className={`text-lg mr-3 ${habit.completed ? 'text-green-600' : 'text-red-500'}`}>
                  {habit.completed ? '✅' : '❌'}
                </Text>
                <Text className="text-gray-700 flex-1">{habit.habit_name}</Text>
              </View>
              <Text className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {habit.habit_category}
              </Text>
            </View>
          ))}
          {todayHabits.length > 5 && (
            <Text className="text-sm text-gray-500 mt-2">
              +{todayHabits.length - 5} more habits tracked
            </Text>
          )}
        </View>
      ) : (
        <Text className="text-gray-500 text-sm">
          No habits tracked today. Write a journal entry to start tracking!
        </Text>
      )}
    </View>
  );

  const renderTopHabits = () => (
    <View className="bg-white rounded-lg p-4 mb-4 shadow-sm border border-gray-100">
      <View className="flex-row items-center justify-between mb-3">
        <Text className="text-lg font-semibold text-gray-800">Top Habits</Text>
        <TouchableOpacity onPress={navigateToHistory}>
          <Text className="text-blue-600 text-sm">View All</Text>
        </TouchableOpacity>
      </View>
      {stats.topHabits.length > 0 ? (
        <View>
          {stats.topHabits.map((habit, index) => (
            <View key={index} className="flex-row items-center justify-between py-2">
              <View className="flex-1">
                <Text className="text-gray-700 font-medium">{habit.habit_name}</Text>
                <Text className="text-xs text-gray-500">{habit.category}</Text>
              </View>
              <View className="items-end">
                <Text className="text-blue-600 font-semibold">{habit.count}</Text>
                <Text className="text-xs text-gray-500">times</Text>
              </View>
            </View>
          ))}
        </View>
      ) : (
        <Text className="text-gray-500 text-sm">
          Start journaling to see your habit patterns!
        </Text>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-gray-50">
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="text-gray-600 mt-2">Loading dashboard...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 px-4 py-2">
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-1">
            Good {new Date().getHours() < 12 ? 'Morning' : new Date().getHours() < 18 ? 'Afternoon' : 'Evening'}!
          </Text>
          <Text className="text-gray-600">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </Text>
        </View>

        {renderQuickStats()}
        {renderTodayStatus()}
        {renderTodayHabits()}
        {renderTopHabits()}
      </ScrollView>
    </SafeAreaView>
  );
};

export default DashboardScreen;
