{"version": 3, "names": ["inheritTrailingComments", "child", "parent", "inherit"], "sources": ["../../src/comments/inheritTrailingComments.ts"], "sourcesContent": ["import inherit from \"../utils/inherit\";\nimport type * as t from \"..\";\n\nexport default function inheritTrailingComments(\n  child: t.Node,\n  parent: t.Node,\n): void {\n  inherit(\"trailingComments\", child, parent);\n}\n"], "mappings": ";;;;;;;AAAA;;AAGe,SAASA,uBAAT,CACbC,KADa,EAEbC,MAFa,EAGP;EACN,IAAAC,gBAAA,EAAQ,kBAAR,EAA4BF,KAA5B,EAAmCC,MAAnC;AACD"}