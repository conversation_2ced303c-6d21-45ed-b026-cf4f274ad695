{"version": 3, "names": ["addComments", "node", "type", "comments", "key", "concat", "push"], "sources": ["../../src/comments/addComments.ts"], "sourcesContent": ["import type * as t from \"..\";\n\n/**\n * Add comments of certain type to a node.\n */\nexport default function addComments<T extends t.Node>(\n  node: T,\n  type: t.CommentTypeShorthand,\n  comments: Array<t.Comment>,\n): T {\n  if (!comments || !node) return node;\n\n  const key = `${type}Comments` as const;\n\n  if (node[key]) {\n    if (type === \"leading\") {\n      node[key] = comments.concat(node[key]);\n    } else {\n      node[key].push(...comments);\n    }\n  } else {\n    node[key] = comments;\n  }\n\n  return node;\n}\n"], "mappings": ";;;;;;;AAKe,SAASA,WAAT,CACbC,IADa,EAEbC,IAFa,EAGbC,QAHa,EAIV;EACH,IAAI,CAACA,QAAD,IAAa,CAACF,IAAlB,EAAwB,OAAOA,IAAP;EAExB,MAAMG,GAAG,GAAI,GAAEF,IAAK,UAApB;;EAEA,IAAID,IAAI,CAACG,GAAD,CAAR,EAAe;IACb,IAAIF,IAAI,KAAK,SAAb,EAAwB;MACtBD,IAAI,CAACG,GAAD,CAAJ,GAAYD,QAAQ,CAACE,MAAT,CAAgBJ,IAAI,CAACG,GAAD,CAApB,CAAZ;IACD,CAFD,MAEO;MACLH,IAAI,CAACG,GAAD,CAAJ,CAAUE,IAAV,CAAe,GAAGH,QAAlB;IACD;EACF,CAND,MAMO;IACLF,IAAI,CAACG,GAAD,CAAJ,GAAYD,QAAZ;EACD;;EAED,OAAOF,IAAP;AACD"}