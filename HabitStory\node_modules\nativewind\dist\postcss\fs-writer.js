"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.outputWriter = void 0;
const node_fs_1 = require("node:fs");
const generator_1 = __importDefault(require("@babel/generator"));
const types_1 = require("@babel/types");
const serialize_1 = require("./serialize");
function outputWriter(filename, values) {
    const { stylesheetCreateExpression } = (0, serialize_1.serializer)(values);
    const imports = (0, types_1.variableDeclaration)("const", [
        (0, types_1.variableDeclarator)((0, types_1.objectPattern)([
            (0, types_1.objectProperty)((0, types_1.identifier)("NativeWindStyleSheet"), (0, types_1.identifier)("_NativeWindStyleSheet")),
        ]), (0, types_1.callExpression)((0, types_1.identifier)("require"), [(0, types_1.stringLiteral)("nativewind")])),
    ]);
    (0, types_1.addComment)(imports, "leading", "This file was generated by NativeWind. Do not edit!");
    const ast = (0, types_1.file)((0, types_1.program)([imports, (0, types_1.expressionStatement)(stylesheetCreateExpression)]));
    (0, node_fs_1.writeFileSync)(filename, (0, generator_1.default)(ast).code);
}
exports.outputWriter = outputWriter;
