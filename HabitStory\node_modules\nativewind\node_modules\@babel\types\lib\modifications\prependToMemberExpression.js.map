{"version": 3, "names": ["prependToMemberExpression", "member", "prepend", "is<PERSON><PERSON><PERSON>", "object", "Error", "memberExpression"], "sources": ["../../src/modifications/prependToMemberExpression.ts"], "sourcesContent": ["import { memberExpression } from \"../builders/generated\";\nimport { isSuper } from \"..\";\nimport type * as t from \"..\";\n\n/**\n * Prepend a node to a member expression.\n */\nexport default function prependToMemberExpression<\n  T extends Pick<t.MemberExpression, \"object\" | \"property\">,\n>(member: T, prepend: t.MemberExpression[\"object\"]): T {\n  if (isSuper(member.object)) {\n    throw new Error(\n      \"Cannot prepend node to super property access (`super.foo`).\",\n    );\n  }\n  member.object = memberExpression(prepend, member.object);\n\n  return member;\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAMe,SAASA,yBAAT,CAEbC,MAFa,EAEFC,OAFE,EAEwC;EACrD,IAAI,IAAAC,SAAA,EAAQF,MAAM,CAACG,MAAf,CAAJ,EAA4B;IAC1B,MAAM,IAAIC,KAAJ,CACJ,6DADI,CAAN;EAGD;;EACDJ,MAAM,CAACG,MAAP,GAAgB,IAAAE,2BAAA,EAAiBJ,OAAjB,EAA0BD,MAAM,CAACG,MAAjC,CAAhB;EAEA,OAAOH,MAAP;AACD"}