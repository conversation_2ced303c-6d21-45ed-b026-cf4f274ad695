{"name": "habitstory", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "parse-test": "node scripts/parse-test.js", "db-reset": "node scripts/reset-db.js", "build": "expo build", "eject": "expo eject"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@types/react-native": "^0.72.8", "axios": "^1.10.0", "expo": "~53.0.16", "expo-device": "^7.1.4", "expo-notifications": "^0.31.3", "expo-secure-store": "^14.2.3", "expo-sqlite": "^15.2.13", "expo-status-bar": "~2.2.3", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-webview": "^13.15.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}