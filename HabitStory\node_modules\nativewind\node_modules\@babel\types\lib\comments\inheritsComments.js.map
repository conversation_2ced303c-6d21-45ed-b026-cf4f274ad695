{"version": 3, "names": ["inheritsComments", "child", "parent", "inheritTrailingComments", "inheritLeadingComments", "inheritInnerComments"], "sources": ["../../src/comments/inheritsComments.ts"], "sourcesContent": ["import inheritTrailingComments from \"./inheritTrailingComments\";\nimport inheritLeadingComments from \"./inheritLeadingComments\";\nimport inheritInnerComments from \"./inheritInnerComments\";\nimport type * as t from \"..\";\n\n/**\n * Inherit all unique comments from `parent` node to `child` node.\n */\nexport default function inheritsComments<T extends t.Node>(\n  child: T,\n  parent: t.Node,\n): T {\n  inheritTrailingComments(child, parent);\n  inheritLeadingComments(child, parent);\n  inheritInnerComments(child, parent);\n\n  return child;\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAMe,SAASA,gBAAT,CACbC,KADa,EAEbC,MAFa,EAGV;EACH,IAAAC,gCAAA,EAAwBF,KAAxB,EAA+BC,MAA/B;EACA,IAAAE,+BAAA,EAAuBH,KAAvB,EAA8BC,MAA9B;EACA,IAAAG,6BAAA,EAAqBJ,KAArB,EAA4BC,MAA5B;EAEA,OAAOD,KAAP;AACD"}