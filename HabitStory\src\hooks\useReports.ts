import { useState, useEffect, useCallback } from 'react';
import { 
  insertReport, 
  getLatestReport, 
  getAllReports, 
  insertFeedback,
  Report 
} from '../lib/db';

export interface UseReportsReturn {
  reports: Report[];
  latestReport: Report | null;
  isLoading: boolean;
  error: string | null;
  createReport: (weekStart: string, weekEnd: string, htmlContent: string) => Promise<number>;
  getRecentReport: () => Promise<Report | null>;
  submitFeedback: (reportId: number, rating: number, text?: string) => Promise<void>;
  refreshReports: () => Promise<void>;
  clearError: () => void;
}

export const useReports = (): UseReportsReturn => {
  const [reports, setReports] = useState<Report[]>([]);
  const [latestReport, setLatestReport] = useState<Report | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshReports = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const allReports = await getAllReports();
      setReports(allReports);
      
      const recent = await getLatestReport();
      setLatestReport(recent);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load reports';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createReport = useCallback(async (
    weekStart: string,
    weekEnd: string,
    htmlContent: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const reportId = await insertReport(weekStart, weekEnd, htmlContent);
      await refreshReports();
      return reportId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create report';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [refreshReports]);

  const getRecentReport = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const report = await getLatestReport();
      setLatestReport(report);
      return report;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get latest report';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const submitFeedback = useCallback(async (
    reportId: number,
    rating: number,
    text?: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      await insertFeedback(reportId, rating, text || null);
      await refreshReports();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to submit feedback';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [refreshReports]);

  // Load reports on mount
  useEffect(() => {
    refreshReports();
  }, [refreshReports]);

  return {
    reports,
    latestReport,
    isLoading,
    error,
    createReport,
    getRecentReport,
    submitFeedback,
    refreshReports,
    clearError,
  };
};
