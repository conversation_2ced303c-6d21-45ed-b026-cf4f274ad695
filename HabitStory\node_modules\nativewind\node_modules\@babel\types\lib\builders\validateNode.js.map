{"version": 3, "names": ["validateNode", "node", "keys", "BUILDER_KEYS", "type", "key", "validate"], "sources": ["../../src/builders/validateNode.ts"], "sourcesContent": ["import validate from \"../validators/validate\";\nimport type * as t from \"..\";\nimport { BUILDER_KEYS } from \"..\";\n\nexport default function validateNode<N extends t.Node>(node: N) {\n  // todo: because keys not in BUILDER_KEYS are not validated - this actually allows invalid nodes in some cases\n  const keys = BUILDER_KEYS[node.type] as (keyof N & string)[];\n  for (const key of keys) {\n    validate(node, key, node[key]);\n  }\n  return node;\n}\n"], "mappings": ";;;;;;;AAAA;;AAEA;;AAEe,SAASA,YAAT,CAAwCC,IAAxC,EAAiD;EAE9D,MAAMC,IAAI,GAAGC,cAAA,CAAaF,IAAI,CAACG,IAAlB,CAAb;;EACA,KAAK,MAAMC,GAAX,IAAkBH,IAAlB,EAAwB;IACtB,IAAAI,iBAAA,EAASL,IAAT,EAAeI,GAAf,EAAoBJ,IAAI,CAACI,GAAD,CAAxB;EACD;;EACD,OAAOJ,IAAP;AACD"}