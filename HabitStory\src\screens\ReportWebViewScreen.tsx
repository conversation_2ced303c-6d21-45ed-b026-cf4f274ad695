import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useReports } from '../hooks/useReports';
import { getAllReports } from '../lib/db';


interface RouteParams {
  reportId: number;
}

const ReportWebViewScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { reportId } = route.params as RouteParams;
  
  const [report, setReport] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [isSubmittingFeedback, setIsSubmittingFeedback] = useState(false);
  
  const { submitFeedback } = useReports();

  useEffect(() => {
    loadReport();
  }, [reportId]);

  const loadReport = async () => {
    setIsLoading(true);
    try {
      const reports = await getAllReports();
      const foundReport = reports.find(r => r.id === reportId);
      setReport(foundReport);
    } catch (error) {
      console.error('Error loading report:', error);
      Alert.alert('Error', 'Failed to load report');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFeedback = async (rating: number) => {
    setIsSubmittingFeedback(true);
    
    try {
      await submitFeedback(reportId, rating, feedbackText.trim() || null);
      
      Alert.alert(
        'Thank You!',
        'Your feedback helps improve future reports.',
        [{ text: 'OK', onPress: () => setShowFeedbackModal(false) }]
      );
      
      // Reload report to show updated feedback
      await loadReport();
    } catch (error) {
      console.error('Error submitting feedback:', error);
      Alert.alert('Error', 'Failed to submit feedback');
    } finally {
      setIsSubmittingFeedback(false);
    }
  };

  const openFeedbackModal = () => {
    setFeedbackText('');
    setShowFeedbackModal(true);
  };

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white justify-center items-center">
        <ActivityIndicator size="large" color="#0ea5e9" />
        <Text className="text-gray-600 mt-4">Loading report...</Text>
      </SafeAreaView>
    );
  }

  if (!report) {
    return (
      <SafeAreaView className="flex-1 bg-white justify-center items-center px-4">
        <Text className="text-gray-800 text-lg text-center mb-4">
          Report not found
        </Text>
        <TouchableOpacity
          className="bg-blue-500 px-6 py-3 rounded-lg"
          onPress={() => navigation.goBack()}
        >
          <Text className="text-white font-semibold">Go Back</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center justify-between px-4 py-2 border-b border-gray-200">
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="p-2"
        >
          <Text className="text-blue-500 font-medium">← Back</Text>
        </TouchableOpacity>
        
        <Text className="text-lg font-semibold text-gray-800">
          Weekly Report
        </Text>
        
        <TouchableOpacity
          onPress={openFeedbackModal}
          className="p-2"
        >
          <Text className="text-purple-500 font-medium">Feedback</Text>
        </TouchableOpacity>
      </View>

      {/* WebView */}
      <WebView
        source={{ html: report.html_content }}
        style={{ flex: 1 }}
        scalesPageToFit={false}
        showsVerticalScrollIndicator={true}
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error('WebView error: ', nativeEvent);
        }}
      />

      {/* Feedback Modal */}
      <Modal
        visible={showFeedbackModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowFeedbackModal(false)}
      >
        <View className="flex-1 bg-black bg-opacity-50 justify-end">
          <View className="bg-white rounded-t-3xl p-6">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold text-gray-800">
                Rate This Report
              </Text>
              <TouchableOpacity
                onPress={() => setShowFeedbackModal(false)}
                className="p-2"
              >
                <Text className="text-gray-500 text-lg">✕</Text>
              </TouchableOpacity>
            </View>

            <Text className="text-gray-600 mb-4">
              How helpful was this weekly summary?
            </Text>

            {/* Rating Buttons */}
            <View className="flex-row justify-center space-x-8 mb-6">
              <TouchableOpacity
                className="items-center p-4"
                onPress={() => handleFeedback(1)}
                disabled={isSubmittingFeedback}
              >
                <Text className="text-4xl mb-2">👍</Text>
                <Text className="text-gray-700 font-medium">Helpful</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                className="items-center p-4"
                onPress={() => handleFeedback(-1)}
                disabled={isSubmittingFeedback}
              >
                <Text className="text-4xl mb-2">👎</Text>
                <Text className="text-gray-700 font-medium">Not Helpful</Text>
              </TouchableOpacity>
            </View>

            {/* Optional Text Feedback */}
            <Text className="text-gray-700 font-medium mb-2">
              Additional feedback (optional):
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-3 text-base mb-4 min-h-[80px]"
              placeholder="What would make future reports better?"
              value={feedbackText}
              onChangeText={setFeedbackText}
              multiline
              textAlignVertical="top"
            />

            {isSubmittingFeedback && (
              <View className="items-center py-4">
                <ActivityIndicator color="#0ea5e9" />
                <Text className="text-gray-600 mt-2">Submitting feedback...</Text>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

export default ReportWebViewScreen;
