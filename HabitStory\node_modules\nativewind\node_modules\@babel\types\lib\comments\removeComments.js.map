{"version": 3, "names": ["removeComments", "node", "COMMENT_KEYS", "for<PERSON>ach", "key"], "sources": ["../../src/comments/removeComments.ts"], "sourcesContent": ["import { COMMENT_KEYS } from \"../constants\";\nimport type * as t from \"..\";\n\n/**\n * Remove comment properties from a node.\n */\nexport default function removeComments<T extends t.Node>(node: T): T {\n  COMMENT_KEYS.forEach(key => {\n    node[key] = null;\n  });\n\n  return node;\n}\n"], "mappings": ";;;;;;;AAAA;;AAMe,SAASA,cAAT,CAA0CC,IAA1C,EAAsD;EACnEC,uBAAA,CAAaC,OAAb,CAAqBC,GAAG,IAAI;IAC1BH,IAAI,CAACG,GAAD,CAAJ,GAAY,IAAZ;EACD,CAFD;;EAIA,OAAOH,IAAP;AACD"}