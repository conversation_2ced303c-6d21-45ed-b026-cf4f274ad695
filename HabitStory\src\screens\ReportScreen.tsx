import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useEntries } from '../hooks/useEntries';
import { useReports } from '../hooks/useReports';
import { useGemini } from '../hooks/useGemini';
import { getWeekDateRange } from '../lib/db';


const ReportScreen: React.FC = () => {
  const navigation = useNavigation();
  const [isGenerating, setIsGenerating] = useState(false);
  const [weekEntries, setWeekEntries] = useState<any[]>([]);
  
  const { getEntriesForWeek } = useEntries();
  const { reports, createReport, refreshReports } = useReports();
  const { generateSummary, error: geminiError } = useGemini();

  useEffect(() => {
    loadWeekEntries();
    refreshReports();
  }, []);

  const loadWeekEntries = async () => {
    try {
      const entries = await getEntriesForWeek(new Date());
      setWeekEntries(entries);
    } catch (error) {
      console.error('Error loading week entries:', error);
    }
  };

  const handleGenerateReport = async () => {
    if (weekEntries.length === 0) {
      Alert.alert(
        'No Entries',
        'You need at least one journal entry this week to generate a report.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsGenerating(true);

    try {
      // Generate the HTML summary
      const htmlContent = await generateSummary(weekEntries);
      
      // Get week date range
      const { start, end } = getWeekDateRange(new Date());
      
      // Save the report
      const reportId = await createReport(start, end, htmlContent);
      
      Alert.alert(
        'Report Generated!',
        'Your personalized weekly summary is ready.',
        [
          {
            text: 'View Report',
            onPress: () => {
              navigation.navigate('ReportWebView' as never, { reportId } as never);
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error generating report:', error);
      Alert.alert(
        'Generation Failed',
        error instanceof Error ? error.message : 'Failed to generate report. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const renderReportCard = (report: any) => {
    const startDate = new Date(report.week_start);
    const endDate = new Date(report.week_end);
    
    return (
      <TouchableOpacity
        key={report.id}
        className="bg-white p-4 rounded-lg border border-gray-200 mb-3"
        onPress={() => {
          navigation.navigate('ReportWebView' as never, { reportId: report.id } as never);
        }}
      >
        <View className="flex-row justify-between items-start mb-2">
          <Text className="text-lg font-semibold text-gray-800">
            Week of {startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
          </Text>
          <Text className="text-gray-500 text-sm">
            {new Date(report.created_at).toLocaleDateString()}
          </Text>
        </View>
        
        <Text className="text-gray-600 mb-2">
          {startDate.toLocaleDateString()} - {endDate.toLocaleDateString()}
        </Text>
        
        {report.feedback_rating && (
          <View className="flex-row items-center">
            <Text className="text-sm text-gray-500">
              Feedback: {report.feedback_rating > 0 ? '👍' : '👎'}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const currentWeekRange = getWeekDateRange(new Date());

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 px-4 py-2">
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Weekly Reports
          </Text>
          <Text className="text-gray-600">
            AI-powered insights from your journal entries
          </Text>
        </View>

        {/* Current Week Section */}
        <View className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-2">
            This Week
          </Text>
          <Text className="text-gray-600 mb-3">
            {new Date(currentWeekRange.start).toLocaleDateString()} - {new Date(currentWeekRange.end).toLocaleDateString()}
          </Text>
          
          <View className="flex-row items-center justify-between mb-4">
            <Text className="text-gray-600">
              {weekEntries.length} {weekEntries.length === 1 ? 'entry' : 'entries'} this week
            </Text>
            {weekEntries.length > 0 && (
              <View className="bg-green-100 px-2 py-1 rounded-full">
                <Text className="text-green-700 text-sm font-medium">Ready</Text>
              </View>
            )}
          </View>

          {geminiError && (
            <View className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
              <Text className="text-red-800 font-medium">Error:</Text>
              <Text className="text-red-700 text-sm">{geminiError}</Text>
            </View>
          )}

          <TouchableOpacity
            className={`rounded-lg py-3 px-4 ${
              isGenerating || weekEntries.length === 0
                ? 'bg-gray-300'
                : 'bg-purple-500'
            }`}
            onPress={handleGenerateReport}
            disabled={isGenerating || weekEntries.length === 0}
          >
            {isGenerating ? (
              <View className="flex-row items-center justify-center">
                <ActivityIndicator color="white" size="small" />
                <Text className="text-white font-semibold ml-2">Generating...</Text>
              </View>
            ) : (
              <Text className="text-white font-semibold text-center">
                Generate Weekly Summary
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Previous Reports */}
        <View>
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Previous Reports
          </Text>
          
          {reports.length === 0 ? (
            <View className="bg-white p-6 rounded-lg border border-gray-200">
              <Text className="text-gray-500 text-center">
                No reports generated yet.{'\n'}Create your first weekly summary above!
              </Text>
            </View>
          ) : (
            reports.map(renderReportCard)
          )}
        </View>

        <View className="h-8" />
      </ScrollView>
    </SafeAreaView>
  );
};

export default ReportScreen;
