import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

// OpenAI API configuration
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';
const API_KEY_STORAGE_KEY = 'openai_api_key';

// Types for OpenAI responses
export interface ParsedEntry {
  habits: string[];
  metrics: {
    sleep_hours?: number;
    water_liters?: number;
    exercise_minutes?: number;
    mood_score?: number;
    stress_level?: number;
    productivity_score?: number;
    social_interactions?: number;
    screen_time_hours?: number;
    meditation_minutes?: number;
    steps?: number;
    [key: string]: any;
  };
  reflection: string;
  user_traits: {
    tone: string;
    style: string;
    traits: Record<string, any>;
  };
}

export interface WeeklySummary {
  html_content: string;
}

// API Key management
export const setApiKey = async (apiKey: string): Promise<void> => {
  try {
    await SecureStore.setItemAsync(API_KEY_STORAGE_KEY, apiKey);
  } catch (error) {
    console.error('Error storing API key:', error);
    throw new Error('Failed to store API key securely');
  }
};

export const getApiKey = async (): Promise<string | null> => {
  try {
    return await SecureStore.getItemAsync(API_KEY_STORAGE_KEY);
  } catch (error) {
    console.error('Error retrieving API key:', error);
    return null;
  }
};

export const removeApiKey = async (): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(API_KEY_STORAGE_KEY);
  } catch (error) {
    console.error('Error removing API key:', error);
  }
};

export const hasApiKey = async (): Promise<boolean> => {
  const apiKey = await getApiKey();
  return apiKey !== null && apiKey.trim().length > 0;
};

// OpenAI API calls
const makeOpenAIRequest = async (
  messages: Array<{ role: string; content: string }>,
  temperature: number = 0.7,
  maxTokens: number = 2000
): Promise<string> => {
  const apiKey = await getApiKey();
  
  if (!apiKey) {
    throw new Error('OpenAI API key not found. Please set your API key in settings.');
  }

  try {
    const response = await axios.post(
      OPENAI_API_URL,
      {
        model: 'gpt-4',
        messages,
        temperature,
        max_tokens: maxTokens,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 seconds timeout
      }
    );

    if (response.data?.choices?.[0]?.message?.content) {
      return response.data.choices[0].message.content.trim();
    } else {
      throw new Error('Invalid response format from OpenAI API');
    }
  } catch (error) {
    console.error('OpenAI API Error:', error);
    
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 401) {
        throw new Error('Invalid API key. Please check your OpenAI API key.');
      } else if (error.response?.status === 429) {
        throw new Error('Rate limit exceeded. Please try again later.');
      } else if (error.response?.status === 500) {
        throw new Error('OpenAI service is temporarily unavailable. Please try again later.');
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout. Please check your internet connection and try again.');
      }
    }
    
    throw new Error('Failed to communicate with OpenAI API. Please try again.');
  }
};

// Parse daily entry with GPT-4
export const parseEntry = async (
  entryText: string,
  previousTraits?: any
): Promise<ParsedEntry> => {
  const prompt = `You are an AI assistant that analyzes daily journal entries to extract structured data. 

Analyze the following journal entry and extract:

1. **habits**: Array of habit-related activities mentioned (e.g., ["morning run", "meditation", "reading"])
2. **metrics**: Object with quantifiable data when mentioned:
   - sleep_hours: Hours of sleep (number)
   - water_liters: Water consumption in liters (number)
   - exercise_minutes: Exercise duration in minutes (number)
   - mood_score: Mood on scale 1-10 (number)
   - stress_level: Stress on scale 1-10 (number)
   - productivity_score: Productivity on scale 1-10 (number)
   - social_interactions: Number of meaningful social interactions (number)
   - screen_time_hours: Screen time in hours (number)
   - meditation_minutes: Meditation time in minutes (number)
   - steps: Number of steps taken (number)
   - Any other quantifiable metrics mentioned
3. **reflection**: A brief 1-2 sentence summary of the main insight or feeling from the entry
4. **user_traits**: Object analyzing the user's communication style:
   - tone: "formal", "informal", "casual", "professional"
   - style: "descriptive", "concise", "emotional", "analytical", "conversational", "poetic"
   - traits: Object with personality traits like {"optimistic": true, "detail_oriented": false, "introspective": true}

Previous user traits for reference: ${previousTraits ? JSON.stringify(previousTraits) : 'None'}

Journal Entry:
"${entryText}"

Respond with ONLY a valid JSON object in this exact format:
{
  "habits": ["habit1", "habit2"],
  "metrics": {"sleep_hours": 8, "mood_score": 7},
  "reflection": "Brief insight from the entry",
  "user_traits": {
    "tone": "informal",
    "style": "conversational", 
    "traits": {"optimistic": true, "detail_oriented": false}
  }
}`;

  const messages = [
    {
      role: 'system',
      content: 'You are a helpful assistant that extracts structured data from journal entries. Always respond with valid JSON only.'
    },
    {
      role: 'user',
      content: prompt
    }
  ];

  try {
    const response = await makeOpenAIRequest(messages, 0.3, 1500);
    
    // Try to parse the JSON response
    const parsed = JSON.parse(response);
    
    // Validate the response structure
    if (!parsed.habits || !Array.isArray(parsed.habits)) {
      parsed.habits = [];
    }
    if (!parsed.metrics || typeof parsed.metrics !== 'object') {
      parsed.metrics = {};
    }
    if (!parsed.reflection || typeof parsed.reflection !== 'string') {
      parsed.reflection = 'No specific reflection extracted.';
    }
    if (!parsed.user_traits) {
      parsed.user_traits = {
        tone: 'informal',
        style: 'conversational',
        traits: {}
      };
    }

    return parsed as ParsedEntry;
  } catch (error) {
    console.error('Error parsing entry:', error);
    
    // Return a fallback response if parsing fails
    return {
      habits: [],
      metrics: {},
      reflection: 'Unable to analyze entry automatically.',
      user_traits: {
        tone: 'informal',
        style: 'conversational',
        traits: {}
      }
    };
  }
};

// Generate weekly summary with GPT-4
export const generateWeeklySummary = async (
  weekEntries: any[],
  userTraits: any,
  previousFeedback?: string
): Promise<WeeklySummary> => {
  const entriesText = weekEntries.map(entry => 
    `Date: ${entry.date}\nText: ${entry.text}\nHabits: ${entry.habits}\nMetrics: ${entry.metrics}\nReflection: ${entry.reflection}`
  ).join('\n\n---\n\n');

  const prompt = `You are an AI life coach creating a personalized weekly summary report. Create an engaging, insightful HTML report based on the user's journal entries.

USER PROFILE:
- Communication Style: ${userTraits?.tone || 'informal'}, ${userTraits?.style || 'conversational'}
- Personality Traits: ${JSON.stringify(userTraits?.traits || {})}

PREVIOUS FEEDBACK: ${previousFeedback || 'None'}

WEEK'S JOURNAL ENTRIES:
${entriesText}

Create a complete HTML document with:
1. **Header**: Personalized title and week summary
2. **Highlights Section**: Key achievements and positive moments
3. **Patterns & Insights**: Behavioral patterns, habit consistency, metric trends
4. **Visual Elements**: Use inline CSS and SVG for simple charts/graphs if metrics are available
5. **Reflection Questions**: 2-3 thoughtful questions for next week
6. **Encouragement**: Personalized motivational message

STYLE REQUIREMENTS:
- Match the user's communication style (${userTraits?.tone || 'informal'} and ${userTraits?.style || 'conversational'})
- Use colors: primary (#0ea5e9), secondary (#d946ef), success (#22c55e)
- Mobile-friendly responsive design
- Include inline CSS (no external stylesheets)
- Add simple SVG visualizations for metrics if data is available
- Keep it engaging and personal, not clinical

Return ONLY the complete HTML document (including <!DOCTYPE html>, <html>, <head>, and <body> tags).`;

  const messages = [
    {
      role: 'system',
      content: 'You are a personalized life coach AI that creates engaging, insightful weekly summary reports in HTML format. Always respond with complete, valid HTML only.'
    },
    {
      role: 'user',
      content: prompt
    }
  ];

  try {
    const htmlContent = await makeOpenAIRequest(messages, 0.7, 4000);
    
    return {
      html_content: htmlContent
    };
  } catch (error) {
    console.error('Error generating weekly summary:', error);
    
    // Return a fallback HTML if generation fails
    const fallbackHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Summary</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        h1 { color: #0ea5e9; text-align: center; }
        .error { color: #ef4444; text-align: center; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Weekly Summary</h1>
        <div class="error">
            <p>Unable to generate personalized summary at this time.</p>
            <p>Please check your internet connection and try again.</p>
        </div>
    </div>
</body>
</html>`;
    
    return {
      html_content: fallbackHtml
    };
  }
};

// Test API connection
export const testApiConnection = async (): Promise<boolean> => {
  try {
    const messages = [
      {
        role: 'user',
        content: 'Hello, please respond with just the word "success" to test the connection.'
      }
    ];
    
    const response = await makeOpenAIRequest(messages, 0, 10);
    return response.toLowerCase().includes('success');
  } catch (error) {
    console.error('API connection test failed:', error);
    return false;
  }
};
