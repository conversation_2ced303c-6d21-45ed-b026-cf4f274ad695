import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const AppNavigator: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>🧪 HabitStory</Text>
      <Text style={styles.subtitle}>Debug: AppNavigator loaded</Text>
      <Text style={styles.debug}>Next: Loading contexts...</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  debug: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
});

export default AppNavigator;
