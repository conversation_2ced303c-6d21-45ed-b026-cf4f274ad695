{"version": 3, "names": ["getQualifiedName", "node", "isIdentifier", "name", "right", "left", "removeTypeDuplicates", "nodes", "generics", "Map", "bases", "typeGroups", "Set", "types", "i", "length", "indexOf", "isTSAnyKeyword", "isTSBaseType", "set", "type", "isTSUnionType", "has", "push", "add", "isTSTypeReference", "typeParameters", "typeName", "existing", "get", "params", "concat", "baseType", "genericName"], "sources": ["../../../src/modifications/typescript/removeTypeDuplicates.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isTSAny<PERSON>eyword,\n  isTSTypeReference,\n  isTSUnionType,\n  isTSBaseType,\n} from \"../../validators/generated\";\nimport type * as t from \"../..\";\n\nfunction getQualifiedName(node: t.TSTypeReference[\"typeName\"]): string {\n  return isIdentifier(node)\n    ? node.name\n    : `${node.right.name}.${getQualifiedName(node.left)}`;\n}\n\n/**\n * Dedupe type annotations.\n */\nexport default function removeTypeDuplicates(\n  nodes: Array<t.TSType>,\n): Array<t.TSType> {\n  const generics = new Map<string, t.TSTypeReference>();\n  const bases = new Map<t.TSBaseType[\"type\"], t.TSBaseType>();\n\n  // store union type groups to circular references\n  const typeGroups = new Set<t.TSType[]>();\n\n  const types: t.TSType[] = [];\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!node) continue;\n\n    // detect duplicates\n    if (types.indexOf(node) >= 0) {\n      continue;\n    }\n\n    // this type matches anything\n    if (isTSAnyKeyword(node)) {\n      return [node];\n    }\n\n    // Analogue of FlowBaseAnnotation\n    if (isTSBaseType(node)) {\n      bases.set(node.type, node);\n      continue;\n    }\n\n    if (isTSUnionType(node)) {\n      if (!typeGroups.has(node.types)) {\n        nodes.push(...node.types);\n        typeGroups.add(node.types);\n      }\n      continue;\n    }\n\n    // todo: support merging tuples: number[]\n    if (isTSTypeReference(node) && node.typeParameters) {\n      const name = getQualifiedName(node.typeName);\n\n      if (generics.has(name)) {\n        let existing: t.TypeScript = generics.get(name);\n        if (existing.typeParameters) {\n          if (node.typeParameters) {\n            existing.typeParameters.params = removeTypeDuplicates(\n              existing.typeParameters.params.concat(node.typeParameters.params),\n            );\n          }\n        } else {\n          existing = node.typeParameters;\n        }\n      } else {\n        generics.set(name, node);\n      }\n\n      continue;\n    }\n\n    types.push(node);\n  }\n\n  // add back in bases\n  for (const [, baseType] of bases) {\n    types.push(baseType);\n  }\n\n  // add back in generics\n  for (const [, genericName] of generics) {\n    types.push(genericName);\n  }\n\n  return types;\n}\n"], "mappings": ";;;;;;;AAAA;;AASA,SAASA,gBAAT,CAA0BC,IAA1B,EAAuE;EACrE,OAAO,IAAAC,uBAAA,EAAaD,IAAb,IACHA,IAAI,CAACE,IADF,GAEF,GAAEF,IAAI,CAACG,KAAL,CAAWD,IAAK,IAAGH,gBAAgB,CAACC,IAAI,CAACI,IAAN,CAAY,EAFtD;AAGD;;AAKc,SAASC,oBAAT,CACbC,KADa,EAEI;EACjB,MAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;EACA,MAAMC,KAAK,GAAG,IAAID,GAAJ,EAAd;EAGA,MAAME,UAAU,GAAG,IAAIC,GAAJ,EAAnB;EAEA,MAAMC,KAAiB,GAAG,EAA1B;;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,KAAK,CAACQ,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;IACrC,MAAMb,IAAI,GAAGM,KAAK,CAACO,CAAD,CAAlB;IACA,IAAI,CAACb,IAAL,EAAW;;IAGX,IAAIY,KAAK,CAACG,OAAN,CAAcf,IAAd,KAAuB,CAA3B,EAA8B;MAC5B;IACD;;IAGD,IAAI,IAAAgB,yBAAA,EAAehB,IAAf,CAAJ,EAA0B;MACxB,OAAO,CAACA,IAAD,CAAP;IACD;;IAGD,IAAI,IAAAiB,uBAAA,EAAajB,IAAb,CAAJ,EAAwB;MACtBS,KAAK,CAACS,GAAN,CAAUlB,IAAI,CAACmB,IAAf,EAAqBnB,IAArB;MACA;IACD;;IAED,IAAI,IAAAoB,wBAAA,EAAcpB,IAAd,CAAJ,EAAyB;MACvB,IAAI,CAACU,UAAU,CAACW,GAAX,CAAerB,IAAI,CAACY,KAApB,CAAL,EAAiC;QAC/BN,KAAK,CAACgB,IAAN,CAAW,GAAGtB,IAAI,CAACY,KAAnB;QACAF,UAAU,CAACa,GAAX,CAAevB,IAAI,CAACY,KAApB;MACD;;MACD;IACD;;IAGD,IAAI,IAAAY,4BAAA,EAAkBxB,IAAlB,KAA2BA,IAAI,CAACyB,cAApC,EAAoD;MAClD,MAAMvB,IAAI,GAAGH,gBAAgB,CAACC,IAAI,CAAC0B,QAAN,CAA7B;;MAEA,IAAInB,QAAQ,CAACc,GAAT,CAAanB,IAAb,CAAJ,EAAwB;QACtB,IAAIyB,QAAsB,GAAGpB,QAAQ,CAACqB,GAAT,CAAa1B,IAAb,CAA7B;;QACA,IAAIyB,QAAQ,CAACF,cAAb,EAA6B;UAC3B,IAAIzB,IAAI,CAACyB,cAAT,EAAyB;YACvBE,QAAQ,CAACF,cAAT,CAAwBI,MAAxB,GAAiCxB,oBAAoB,CACnDsB,QAAQ,CAACF,cAAT,CAAwBI,MAAxB,CAA+BC,MAA/B,CAAsC9B,IAAI,CAACyB,cAAL,CAAoBI,MAA1D,CADmD,CAArD;UAGD;QACF,CAND,MAMO;UACLF,QAAQ,GAAG3B,IAAI,CAACyB,cAAhB;QACD;MACF,CAXD,MAWO;QACLlB,QAAQ,CAACW,GAAT,CAAahB,IAAb,EAAmBF,IAAnB;MACD;;MAED;IACD;;IAEDY,KAAK,CAACU,IAAN,CAAWtB,IAAX;EACD;;EAGD,KAAK,MAAM,GAAG+B,QAAH,CAAX,IAA2BtB,KAA3B,EAAkC;IAChCG,KAAK,CAACU,IAAN,CAAWS,QAAX;EACD;;EAGD,KAAK,MAAM,GAAGC,WAAH,CAAX,IAA8BzB,QAA9B,EAAwC;IACtCK,KAAK,CAACU,IAAN,CAAWU,WAAX;EACD;;EAED,OAAOpB,KAAP;AACD"}