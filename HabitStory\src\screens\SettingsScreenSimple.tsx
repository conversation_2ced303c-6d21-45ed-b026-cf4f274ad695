import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as SecureStore from 'expo-secure-store';
import { testApiConnection } from '../lib/gemini';

const SettingsScreenSimple: React.FC = () => {
  const [apiKey, setApiKey] = useState('');
  const [userName, setUserName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const storedApiKey = await SecureStore.getItemAsync('gemini_api_key');
      const storedUserName = await SecureStore.getItemAsync('user_name');

      if (storedApiKey) {
        setApiKey(storedApiKey);
      }
      if (storedUserName) {
        setUserName(storedUserName);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const handleSaveSettings = async () => {
    if (!userName.trim()) {
      Alert.alert('Missing Name', 'Please enter your name.');
      return;
    }

    if (!apiKey.trim()) {
      Alert.alert('Missing API Key', 'Please enter your Gemini API key.');
      return;
    }

    setIsSaving(true);

    try {
      await Promise.all([
        SecureStore.setItemAsync('user_name', userName.trim()),
        SecureStore.setItemAsync('gemini_api_key', apiKey.trim())
      ]);

      Alert.alert(
        'Settings Saved',
        'Your settings have been updated successfully.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Save Failed',
        'Failed to save settings',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestConnection = async () => {
    if (!apiKey.trim()) {
      Alert.alert('Missing API Key', 'Please enter your Gemini API key first.');
      return;
    }

    setIsTesting(true);

    try {
      // Temporarily save the API key for testing
      await SecureStore.setItemAsync('gemini_api_key', apiKey.trim());

      const isConnected = await testApiConnection();

      if (isConnected) {
        Alert.alert(
          'Connection Successful! ✅',
          'Your Gemini API key is working correctly.',
          [{ text: 'Great!' }]
        );
      } else {
        Alert.alert(
          'Connection Failed ❌',
          'Unable to connect to Gemini. Please check your API key and internet connection.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Test Failed',
        'Error testing connection. Please check your API key.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsTesting(false);
    }
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear Data',
      'Are you sure you want to clear all your data? This will remove your API key and settings from this device.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: async () => {
            try {
              await SecureStore.deleteItemAsync('gemini_api_key');
              await SecureStore.deleteItemAsync('user_name');
              setApiKey('');
              setUserName('');
              Alert.alert('Success', 'All data has been cleared.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data');
            }
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.content}>
          <Text style={styles.title}>⚙️ Settings</Text>
          <Text style={styles.subtitle}>Configure your account and API settings</Text>

          {/* User Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>User Information</Text>

            <Text style={styles.label}>Name</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your name"
              value={userName}
              onChangeText={setUserName}
              autoCapitalize="words"
            />
          </View>

          {/* API Configuration */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Google Gemini API Configuration</Text>

            <Text style={styles.label}>API Key</Text>
            <TextInput
              style={styles.input}
              placeholder="Enter your Gemini API key"
              value={apiKey}
              onChangeText={setApiKey}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
            />
            <Text style={styles.helpText}>
              Get your API key from https://aistudio.google.com/app/apikey
            </Text>
          </View>

          {/* Action Buttons */}
          <TouchableOpacity
            style={[
              styles.button,
              styles.saveButton,
              (isSaving || !userName.trim() || !apiKey.trim()) && styles.buttonDisabled
            ]}
            onPress={handleSaveSettings}
            disabled={isSaving || !userName.trim() || !apiKey.trim()}
          >
            <Text style={styles.buttonText}>
              {isSaving ? 'Saving...' : 'Save Settings'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.button,
              styles.testButton,
              (!apiKey.trim() || isTesting) && styles.buttonDisabled
            ]}
            onPress={handleTestConnection}
            disabled={!apiKey.trim() || isTesting}
          >
            <Text style={styles.buttonText}>
              {isTesting ? 'Testing...' : 'Test API Connection'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.clearButton]}
            onPress={handleClearData}
          >
            <Text style={styles.buttonText}>Clear All Data</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 24,
  },
  section: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
    backgroundColor: 'white',
  },
  helpText: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: -8,
    marginBottom: 8,
  },
  button: {
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginBottom: 16,
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: '#10b981',
  },
  testButton: {
    backgroundColor: '#0ea5e9',
  },
  clearButton: {
    backgroundColor: '#ef4444',
  },
  buttonDisabled: {
    backgroundColor: '#d1d5db',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SettingsScreenSimple;
