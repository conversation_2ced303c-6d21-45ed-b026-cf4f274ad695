import { useState, useEffect, useCallback } from 'react';
import { 
  insertHabitTracking, 
  getHabitsForDate, 
  getHabitHistory, 
  getHabitStreak,
  getAllUniqueHabits,
  HabitTracking 
} from '../lib/db';

export interface UseHabitTrackingReturn {
  todayHabits: HabitTracking[];
  allHabits: { habit_name: string; habit_category: string; count: number }[];
  isLoading: boolean;
  error: string | null;
  trackHabit: (habitName: string, category: string, date: string, completed?: boolean, confidence?: number) => Promise<void>;
  getHabitsForDay: (date: string) => Promise<HabitTracking[]>;
  getHabitStreak: (habitName: string) => Promise<number>;
  getHabitHistory: (habitName: string, days?: number) => Promise<HabitTracking[]>;
  refreshHabits: () => Promise<void>;
  clearError: () => void;
}

export const useHabitTracking = (): UseHabitTrackingReturn => {
  const [todayHabits, setTodayHabits] = useState<HabitTracking[]>([]);
  const [allHabits, setAllHabits] = useState<{ habit_name: string; habit_category: string; count: number }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  const refreshHabits = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const today = formatDate(new Date());
      const [todayHabitsData, allHabitsData] = await Promise.all([
        getHabitsForDate(today),
        getAllUniqueHabits()
      ]);
      
      setTodayHabits(todayHabitsData);
      setAllHabits(allHabitsData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load habits';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const trackHabit = useCallback(async (
    habitName: string,
    category: string,
    date: string,
    completed: boolean = true,
    confidence: number = 1.0
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      await insertHabitTracking(habitName, category, date, completed, confidence);
      await refreshHabits();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to track habit';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [refreshHabits]);

  const getHabitsForDay = useCallback(async (date: string): Promise<HabitTracking[]> => {
    setError(null);
    try {
      return await getHabitsForDate(date);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get habits for date';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const getStreakForHabit = useCallback(async (habitName: string): Promise<number> => {
    setError(null);
    try {
      return await getHabitStreak(habitName);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get habit streak';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const getHistoryForHabit = useCallback(async (habitName: string, days: number = 30): Promise<HabitTracking[]> => {
    setError(null);
    try {
      return await getHabitHistory(habitName, days);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get habit history';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Load initial data
  useEffect(() => {
    refreshHabits();
  }, [refreshHabits]);

  return {
    todayHabits,
    allHabits,
    isLoading,
    error,
    trackHabit,
    getHabitsForDay,
    getHabitStreak: getStreakForHabit,
    getHabitHistory: getHistoryForHabit,
    refreshHabits,
    clearError,
  };
};
