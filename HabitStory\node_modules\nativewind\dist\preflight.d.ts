/// <reference types="react" />
import { View as RNView } from "react-native";
/**
 * Hello!
 *
 * These are undocumented pre-styled components.
 *
 * The reason why they are undocumented, is that they will eventually have
 * base styles, similar to Tailwind Preflight.
 *
 * We're still undecided on what this should be, but if you'd like to start please
 * open a PR or a Github discussion
 */
export declare const ActivityIndicator: import("react").ForwardRefExoticComponent<import("react-native").ActivityIndicatorProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const FlatList: import("react").ForwardRefExoticComponent<import("react-native").FlatListProps<unknown> & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const Image: import("react").ForwardRefExoticComponent<import("react-native").ImageProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const ImageBackground: import("react").ForwardRefExoticComponent<import("react-native").ImageBackgroundProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const KeyboardAvoidingView: import("react").ForwardRefExoticComponent<import("react-native").KeyboardAvoidingViewProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const Modal: import("react").ForwardRefExoticComponent<import("react-native").ModalBaseProps & import("react-native").ModalPropsIOS & import("react-native").ModalPropsAndroid & import("react-native").ViewProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const Pressable: import("react").ForwardRefExoticComponent<Pick<import("./styled").StyledProps<import("react-native").PressableProps & import("react").RefAttributes<RNView>>, "key" | "className" | "tw" | "baseClassName" | "baseTw" | keyof import("react-native").PressableProps> & import("react").RefAttributes<RNView>>;
export declare const RefreshControl: import("react").ForwardRefExoticComponent<import("react-native").RefreshControlProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const ScrollView: import("react").ForwardRefExoticComponent<import("react-native").ScrollViewProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const SectionList: import("react").ForwardRefExoticComponent<import("react-native").SectionListProps<unknown, unknown> & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const Switch: import("react").ForwardRefExoticComponent<import("react-native").SwitchProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const Text: import("react").ForwardRefExoticComponent<import("react-native").TextProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const TextInput: import("react").ForwardRefExoticComponent<import("react-native").TextInputProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const TouchableHighlight: import("react").ForwardRefExoticComponent<import("react-native").TouchableHighlightProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const TouchableOpacity: import("react").ForwardRefExoticComponent<import("react-native").TouchableOpacityProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const TouchableWithoutFeedback: import("react").ForwardRefExoticComponent<import("react-native").TouchableWithoutFeedbackProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const View: import("react").ForwardRefExoticComponent<import("react-native").ViewProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const VirtualizedList: import("react").ForwardRefExoticComponent<import("react-native").VirtualizedListProps<unknown> & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const DrawerLayoutAndroid: import("react").ForwardRefExoticComponent<import("react-native").DrawerLayoutAndroidProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const TouchableNativeFeedback: import("react").ForwardRefExoticComponent<import("react-native").TouchableNativeFeedbackProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const InputAccessoryView: import("react").ForwardRefExoticComponent<import("react-native").InputAccessoryViewProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
export declare const SafeAreaView: import("react").ForwardRefExoticComponent<import("react-native").ViewProps & {
    className?: string | undefined;
    tw?: string | undefined;
    baseClassName?: string | undefined;
    baseTw?: string | undefined;
} & import("react").RefAttributes<unknown>>;
