{"version": 3, "file": "Device.types.js", "sourceRoot": "", "sources": ["../src/Device.types.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAN,IAAY,UAqBX;AArBD,WAAY,UAAU;IACpB;;OAEG;IACH,iDAAW,CAAA;IACX;;OAEG;IACH,6CAAK,CAAA;IACL;;OAEG;IACH,+CAAM,CAAA;IACN;;OAEG;IACH,iDAAO,CAAA;IACP;;OAEG;IACH,uCAAE,CAAA;AACJ,CAAC,EArBW,UAAU,KAAV,UAAU,QAqBrB", "sourcesContent": ["/**\n * An enum representing the different types of devices supported by Expo.\n */\nexport enum DeviceType {\n  /**\n   * An unrecognized device type.\n   */\n  UNKNOWN = 0,\n  /**\n   * Mobile phone handsets, typically with a touch screen and held in one hand.\n   */\n  PHONE,\n  /**\n   * Tablet computers, typically with a touch screen that is larger than a usual phone.\n   */\n  TABLET,\n  /**\n   * Desktop or laptop computers, typically with a keyboard and mouse.\n   */\n  DESKTOP,\n  /**\n   * Device with TV-based interfaces.\n   */\n  TV,\n}\n"]}