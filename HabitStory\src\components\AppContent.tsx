import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { useApp } from '../context/AppContext';
import TabNavigator from './TabNavigator';
import ReportWebViewScreenSimple from '../screens/ReportWebViewScreenSimple';
import LoginScreenSimple from '../screens/LoginScreenSimple';
import LoadingScreen from '../screens/LoadingScreen';

const Stack = createStackNavigator();

const AppContent: React.FC = () => {
  const { isInitialized } = useApp();

  // Show loading screen while app is initializing
  if (!isInitialized) {
    return <LoadingScreen />;
  }

  // For now, show the main app without authentication
  // We'll add authentication back later
  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Main" component={TabNavigator} />
        <Stack.Screen
          name="ReportWebView"
          component={ReportWebViewScreenSimple}
          options={{
            headerShown: false,
            presentation: 'modal',
          }}
        />
        <Stack.Screen name="Login" component={LoginScreenSimple} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppContent;
