// Prompt templates for OpenAI interactions

export interface PromptVariables {
  entryText?: string;
  previousTraits?: any;
  weekEntries?: any[];
  userTraits?: any;
  previousFeedback?: string;
  userName?: string;
}

// Template for parsing daily journal entries
export const PARSE_PROMPT = (variables: PromptVariables): string => {
  const { entryText, previousTraits } = variables;
  
  return `You are an expert AI assistant specialized in analyzing personal journal entries to extract meaningful, structured data. Your goal is to help users understand their daily patterns and growth.

ANALYSIS TASK:
Extract structured data from the following journal entry with high accuracy and insight.

EXTRACTION REQUIREMENTS:

1. **habits** (Array): Identify specific habit-related activities mentioned or implied
   - Include both explicit habits ("I meditated for 20 minutes") and implicit ones ("felt calm after my morning routine")
   - Focus on recurring, intentional behaviors
   - Examples: ["morning meditation", "evening walk", "journaling", "reading before bed"]

2. **metrics** (Object): Extract quantifiable data when mentioned or reasonably inferred
   Available metrics:
   - sleep_hours: Hours of sleep (number, 0-24)
   - water_liters: Water consumption (number, 0-10)
   - exercise_minutes: Physical activity duration (number, 0-300)
   - mood_score: Overall mood (number, 1-10, where 1=very negative, 10=very positive)
   - stress_level: Stress intensity (number, 1-10, where 1=very relaxed, 10=very stressed)
   - productivity_score: How productive they felt (number, 1-10)
   - social_interactions: Meaningful social connections (number, 0-20)
   - screen_time_hours: Digital device usage (number, 0-24)
   - meditation_minutes: Mindfulness/meditation time (number, 0-120)
   - steps: Physical steps taken (number, 0-50000)
   - energy_level: Physical/mental energy (number, 1-10)
   - gratitude_items: Number of things they're grateful for (number, 0-10)

3. **reflection** (String): Create a concise, insightful summary (1-2 sentences)
   - Capture the main emotional tone and key insight
   - Use the user's voice and style
   - Focus on growth, learning, or significant moments

4. **user_traits** (Object): Analyze communication and personality patterns
   - tone: "formal" | "informal" | "casual" | "professional" | "playful" | "serious"
   - style: "descriptive" | "concise" | "emotional" | "analytical" | "conversational" | "poetic" | "humorous"
   - traits: Object with boolean/string values for personality characteristics
     Examples: {"optimistic": true, "detail_oriented": false, "introspective": true, "goal_oriented": true, "creative": true}

CONTEXT:
Previous user traits: ${previousTraits ? JSON.stringify(previousTraits, null, 2) : 'None available'}

JOURNAL ENTRY TO ANALYZE:
"${entryText}"

RESPONSE FORMAT:
Respond with ONLY a valid JSON object. No additional text, explanations, or markdown formatting.

{
  "habits": ["specific_habit_1", "specific_habit_2"],
  "metrics": {
    "sleep_hours": 8,
    "mood_score": 7,
    "energy_level": 6
  },
  "reflection": "Concise insight capturing the essence of the day and any growth or learning.",
  "user_traits": {
    "tone": "informal",
    "style": "conversational",
    "traits": {
      "optimistic": true,
      "detail_oriented": false,
      "introspective": true
    }
  }
}`;
};

// Template for generating weekly summaries
export const SUMMARY_PROMPT = (variables: PromptVariables): string => {
  const { weekEntries, userTraits, previousFeedback, userName } = variables;
  
  const entriesText = weekEntries?.map(entry => 
    `📅 ${entry.date}\n💭 ${entry.text}\n🎯 Habits: ${entry.habits}\n📊 Metrics: ${entry.metrics}\n🔍 Reflection: ${entry.reflection}`
  ).join('\n\n---\n\n') || '';

  return `You are an expert AI life coach creating a deeply personalized, engaging weekly summary report. Your goal is to provide meaningful insights, celebrate progress, and inspire continued growth.

USER PROFILE:
${userName ? `Name: ${userName}` : ''}
Communication Style: ${userTraits?.tone || 'informal'} tone, ${userTraits?.style || 'conversational'} style
Personality Traits: ${JSON.stringify(userTraits?.traits || {}, null, 2)}

FEEDBACK FROM PREVIOUS REPORTS:
${previousFeedback || 'No previous feedback available'}

THIS WEEK'S JOURNAL ENTRIES:
${entriesText}

CREATE A COMPLETE HTML REPORT WITH:

1. **PERSONALIZED HEADER**
   - Engaging title that reflects the week's theme
   - Brief, encouraging opening that acknowledges their journey

2. **WEEK HIGHLIGHTS** 
   - Celebrate specific achievements and positive moments
   - Acknowledge challenges overcome
   - Use encouraging, personalized language

3. **PATTERNS & INSIGHTS**
   - Identify behavioral patterns and trends
   - Analyze habit consistency and evolution
   - Highlight correlations between activities and mood/energy
   - Present insights in their communication style

4. **VISUAL ELEMENTS**
   - Create simple, beautiful SVG charts for metrics (if data available)
   - Use progress bars, line charts, or simple visualizations
   - Color scheme: Primary (#0ea5e9), Secondary (#d946ef), Success (#22c55e), Warning (#f59e0b)

5. **GROWTH OPPORTUNITIES**
   - Gentle suggestions for improvement
   - Build on existing strengths
   - Frame challenges as opportunities

6. **REFLECTION QUESTIONS**
   - 2-3 thoughtful questions for the upcoming week
   - Tailored to their personality and current focus areas

7. **MOTIVATIONAL CLOSE**
   - Personalized encouragement
   - Acknowledge their commitment to growth
   - Set positive expectations for the week ahead

DESIGN REQUIREMENTS:
- Mobile-first responsive design
- Clean, modern aesthetic with plenty of white space
- Inline CSS only (no external stylesheets)
- Smooth color gradients and subtle shadows
- Typography hierarchy with clear sections
- Accessible color contrasts
- Interactive elements where appropriate

TONE & STYLE MATCHING:
- Mirror their ${userTraits?.tone || 'informal'} tone exactly
- Use their ${userTraits?.style || 'conversational'} communication style
- Incorporate their personality traits naturally
- Avoid clinical or overly formal language unless they prefer it
- Make it feel like a friend who knows them well is writing

TECHNICAL SPECIFICATIONS:
- Complete HTML5 document with proper DOCTYPE
- Responsive viewport meta tag
- All styles inline within <style> tags
- SVG graphics embedded directly
- No external dependencies
- Optimized for mobile viewing
- Cross-browser compatible

Return ONLY the complete HTML document. No additional text or explanations.`;
};

// Template for feedback incorporation
export const FEEDBACK_PROMPT = (variables: PromptVariables): string => {
  const { previousFeedback, userTraits } = variables;
  
  return `You are analyzing user feedback to improve future weekly summary reports.

USER TRAITS: ${JSON.stringify(userTraits, null, 2)}

FEEDBACK RECEIVED: "${previousFeedback}"

Provide a brief analysis of what the user liked/disliked and how to adjust future reports accordingly. Focus on:
- Tone and style preferences
- Content depth and focus areas
- Visual presentation preferences
- Motivational approach effectiveness

Respond with a concise summary of adjustments to make for future reports.`;
};

// Template for habit suggestions
export const HABIT_SUGGESTION_PROMPT = (variables: PromptVariables): string => {
  const { weekEntries, userTraits } = variables;
  
  return `Based on the user's journal entries and personality traits, suggest 3-5 new habits that would complement their current lifestyle and goals.

USER TRAITS: ${JSON.stringify(userTraits, null, 2)}

RECENT ENTRIES: ${weekEntries?.map(e => e.text).join(' | ') || ''}

Provide personalized habit suggestions with:
- Brief explanation of why each habit fits their personality
- How to integrate it into their current routine
- Expected benefits based on their goals

Format as a simple JSON array of objects with "habit", "reason", and "integration" fields.`;
};

// Template for mood analysis
export const MOOD_ANALYSIS_PROMPT = (variables: PromptVariables): string => {
  const { weekEntries } = variables;
  
  return `Analyze the mood patterns from this week's journal entries and provide insights about emotional trends, triggers, and recommendations.

ENTRIES: ${weekEntries?.map(e => `${e.date}: ${e.text}`).join('\n') || ''}

Provide:
1. Overall mood trend for the week
2. Identified mood triggers (positive and negative)
3. Patterns related to activities, sleep, or other factors
4. Gentle recommendations for emotional well-being

Format as JSON with "trend", "triggers", "patterns", and "recommendations" fields.`;
};

// Utility function to validate prompt variables
export const validatePromptVariables = (
  template: string,
  variables: PromptVariables
): { isValid: boolean; missingVariables: string[] } => {
  const requiredVars: { [key: string]: string[] } = {
    PARSE_PROMPT: ['entryText'],
    SUMMARY_PROMPT: ['weekEntries', 'userTraits'],
    FEEDBACK_PROMPT: ['previousFeedback', 'userTraits'],
    HABIT_SUGGESTION_PROMPT: ['weekEntries', 'userTraits'],
    MOOD_ANALYSIS_PROMPT: ['weekEntries']
  };

  const templateName = Object.keys(requiredVars).find(name => 
    template.includes(name.replace('_PROMPT', ''))
  );

  if (!templateName) {
    return { isValid: true, missingVariables: [] };
  }

  const required = requiredVars[templateName];
  const missing = required.filter(varName => 
    !variables[varName as keyof PromptVariables]
  );

  return {
    isValid: missing.length === 0,
    missingVariables: missing
  };
};

// Helper function to truncate long entries for prompts
export const truncateEntry = (text: string, maxLength: number = 2000): string => {
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength - 3) + '...';
};

// Helper function to format entries for prompts
export const formatEntriesForPrompt = (entries: any[]): string => {
  return entries.map((entry, index) => 
    `Entry ${index + 1} (${entry.date}):\n${truncateEntry(entry.text, 500)}\n`
  ).join('\n');
};
