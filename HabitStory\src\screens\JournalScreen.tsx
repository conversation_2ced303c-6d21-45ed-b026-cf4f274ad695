import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import '../lib/global.css';
import { useOpenAI } from '../hooks/useOpenAI';
import { useEntries } from '../hooks/useEntries';
import { upsertTraits, formatDate } from '../lib/db';

const JournalScreen: React.FC = () => {
  const [entryText, setEntryText] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [todayEntry, setTodayEntry] = useState<any>(null);
  
  const { parseEntryText, error: openAIError, clearError } = useOpenAI();
  const { addEntry, getEntryByDate, editEntry, error: entriesError } = useEntries();

  const today = formatDate(new Date());

  useEffect(() => {
    loadTodayEntry();
  }, []);

  const loadTodayEntry = async () => {
    try {
      const entry = await getEntryByDate(today);
      if (entry) {
        setTodayEntry(entry);
        setEntryText(entry.text);
      }
    } catch (error) {
      console.error('Error loading today\'s entry:', error);
    }
  };

  const handleAnalyze = async () => {
    if (!entryText.trim()) {
      Alert.alert('Empty Entry', 'Please write something before analyzing.');
      return;
    }

    setIsAnalyzing(true);
    clearError();

    try {
      // Parse the entry with GPT-4
      const parsed = await parseEntryText(entryText.trim());
      
      // Update user traits
      await upsertTraits(
        parsed.user_traits.tone,
        parsed.user_traits.style,
        parsed.user_traits.traits
      );

      // Save or update the entry
      if (todayEntry) {
        await editEntry(
          todayEntry.id,
          entryText.trim(),
          parsed.habits,
          parsed.metrics,
          parsed.reflection
        );
      } else {
        await addEntry(
          today,
          entryText.trim(),
          parsed.habits,
          parsed.metrics,
          parsed.reflection
        );
      }

      // Reload today's entry
      await loadTodayEntry();

      Alert.alert(
        'Analysis Complete!',
        `Found ${parsed.habits.length} habits and ${Object.keys(parsed.metrics).length} metrics.\n\nReflection: ${parsed.reflection}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error analyzing entry:', error);
      Alert.alert(
        'Analysis Failed',
        error instanceof Error ? error.message : 'Failed to analyze entry. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsAnalyzing(false);
    }
  };

  const renderAnalysisResults = () => {
    if (!todayEntry) return null;

    const habits = JSON.parse(todayEntry.habits || '[]');
    const metrics = JSON.parse(todayEntry.metrics || '{}');

    return (
      <View className="bg-blue-50 p-4 rounded-lg mb-4">
        <Text className="text-lg font-semibold text-blue-800 mb-2">Today's Analysis</Text>
        
        {habits.length > 0 && (
          <View className="mb-3">
            <Text className="font-medium text-blue-700 mb-1">Habits Identified:</Text>
            {habits.map((habit: string, index: number) => (
              <Text key={index} className="text-blue-600 ml-2">• {habit}</Text>
            ))}
          </View>
        )}

        {Object.keys(metrics).length > 0 && (
          <View className="mb-3">
            <Text className="font-medium text-blue-700 mb-1">Metrics Tracked:</Text>
            {Object.entries(metrics).map(([key, value]) => (
              <Text key={key} className="text-blue-600 ml-2">
                • {key.replace('_', ' ')}: {value}
              </Text>
            ))}
          </View>
        )}

        {todayEntry.reflection && (
          <View>
            <Text className="font-medium text-blue-700 mb-1">Reflection:</Text>
            <Text className="text-blue-600 italic">"{todayEntry.reflection}"</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1 px-4 py-2">
          <View className="mb-6">
            <Text className="text-2xl font-bold text-gray-800 mb-2">
              Daily Journal
            </Text>
            <Text className="text-gray-600">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </Text>
          </View>

          {renderAnalysisResults()}

          <View className="mb-6">
            <Text className="text-lg font-semibold text-gray-800 mb-3">
              How was your day?
            </Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-4 text-base min-h-[200px] bg-gray-50"
              placeholder="Write about your day... Include activities, feelings, habits, sleep, exercise, or anything meaningful to you."
              value={entryText}
              onChangeText={setEntryText}
              multiline
              textAlignVertical="top"
              style={{ fontSize: 16, lineHeight: 24 }}
            />
          </View>

          {(openAIError || entriesError) && (
            <View className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <Text className="text-red-800 font-medium">Error:</Text>
              <Text className="text-red-700">{openAIError || entriesError}</Text>
            </View>
          )}

          <TouchableOpacity
            className={`rounded-lg py-4 px-6 ${
              isAnalyzing || !entryText.trim()
                ? 'bg-gray-300'
                : 'bg-blue-500'
            }`}
            onPress={handleAnalyze}
            disabled={isAnalyzing || !entryText.trim()}
          >
            {isAnalyzing ? (
              <View className="flex-row items-center justify-center">
                <ActivityIndicator color="white" size="small" />
                <Text className="text-white font-semibold ml-2">Analyzing...</Text>
              </View>
            ) : (
              <Text className="text-white font-semibold text-center text-lg">
                {todayEntry ? 'Update Analysis' : 'Analyze Entry'}
              </Text>
            )}
          </TouchableOpacity>

          <View className="h-8" />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default JournalScreen;
