{"version": 3, "names": ["validate", "node", "key", "val", "fields", "NODE_FIELDS", "type", "field", "validateField", "validate<PERSON><PERSON><PERSON>", "optional", "NODE_PARENT_VALIDATIONS"], "sources": ["../../src/validators/validate.ts"], "sourcesContent": ["import {\n  NODE_FIELDS,\n  NODE_PARENT_VALIDATIONS,\n  type FieldOptions,\n} from \"../definitions\";\nimport type * as t from \"..\";\n\nexport default function validate(\n  node: t.Node | undefined | null,\n  key: string,\n  val: any,\n): void {\n  if (!node) return;\n\n  const fields = NODE_FIELDS[node.type];\n  if (!fields) return;\n\n  const field = fields[key];\n  validateField(node, key, val, field);\n  validateChild(node, key, val);\n}\n\nexport function validateField(\n  node: t.Node | undefined | null,\n  key: string,\n  val: any,\n  field: FieldOptions | undefined | null,\n): void {\n  if (!field?.validate) return;\n  if (field.optional && val == null) return;\n\n  field.validate(node, key, val);\n}\n\nexport function validateChild(\n  node: t.Node | undefined | null,\n  key: string,\n  val?: t.Node | undefined | null,\n) {\n  if (val == null) return;\n  const validate = NODE_PARENT_VALIDATIONS[val.type];\n  if (!validate) return;\n  validate(node, key, val);\n}\n"], "mappings": ";;;;;;;;;AAAA;;AAOe,SAASA,QAAT,CACbC,IADa,EAEbC,GAFa,EAGbC,GAHa,EAIP;EACN,IAAI,CAACF,IAAL,EAAW;EAEX,MAAMG,MAAM,GAAGC,wBAAA,CAAYJ,IAAI,CAACK,IAAjB,CAAf;EACA,IAAI,CAACF,MAAL,EAAa;EAEb,MAAMG,KAAK,GAAGH,MAAM,CAACF,GAAD,CAApB;EACAM,aAAa,CAACP,IAAD,EAAOC,GAAP,EAAYC,GAAZ,EAAiBI,KAAjB,CAAb;EACAE,aAAa,CAACR,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAb;AACD;;AAEM,SAASK,aAAT,CACLP,IADK,EAELC,GAFK,EAGLC,GAHK,EAILI,KAJK,EAKC;EACN,IAAI,EAACA,KAAD,YAACA,KAAK,CAAEP,QAAR,CAAJ,EAAsB;EACtB,IAAIO,KAAK,CAACG,QAAN,IAAkBP,GAAG,IAAI,IAA7B,EAAmC;EAEnCI,KAAK,CAACP,QAAN,CAAeC,IAAf,EAAqBC,GAArB,EAA0BC,GAA1B;AACD;;AAEM,SAASM,aAAT,CACLR,IADK,EAELC,GAFK,EAGLC,GAHK,EAIL;EACA,IAAIA,GAAG,IAAI,IAAX,EAAiB;EACjB,MAAMH,QAAQ,GAAGW,oCAAA,CAAwBR,GAAG,CAACG,IAA5B,CAAjB;EACA,IAAI,CAACN,QAAL,EAAe;EACfA,QAAQ,CAACC,IAAD,EAAOC,GAAP,EAAYC,GAAZ,CAAR;AACD"}