import React from 'react';
import { View, Text, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import '../lib/global.css';

const LoadingScreen: React.FC = () => {
  return (
    <SafeAreaView className="flex-1 bg-white justify-center items-center">
      <View className="items-center">
        <Text className="text-4xl mb-6">📖</Text>
        <Text className="text-2xl font-bold text-gray-800 mb-4">
          HabitStory
        </Text>
        <ActivityIndicator size="large" color="#0ea5e9" />
        <Text className="text-gray-600 mt-4">
          Initializing your journal...
        </Text>
      </View>
    </SafeAreaView>
  );
};

export default LoadingScreen;
