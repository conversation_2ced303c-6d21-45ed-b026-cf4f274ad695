import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../context/AuthContext';
import '../lib/global.css';

const LoginScreen: React.FC = () => {
  const [name, setName] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { login } = useAuth();

  const handleLogin = async () => {
    if (!name.trim()) {
      Alert.alert('Missing Name', 'Please enter your name.');
      return;
    }

    if (!apiKey.trim()) {
      Alert.alert('Missing API Key', 'Please enter your OpenAI API key.');
      return;
    }

    if (!apiKey.startsWith('sk-')) {
      Alert.alert('Invalid API Key', 'OpenAI API keys start with "sk-".');
      return;
    }

    setIsLoading(true);

    try {
      await login(name.trim(), apiKey.trim());
    } catch (error) {
      Alert.alert(
        'Login Failed',
        error instanceof Error ? error.message : 'Failed to save your information. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gradient-to-br from-blue-50 to-purple-50">
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1 px-6 py-8">
          {/* Header */}
          <View className="items-center mb-12">
            <Text className="text-4xl mb-4">📖</Text>
            <Text className="text-3xl font-bold text-gray-800 mb-2">
              HabitStory
            </Text>
            <Text className="text-gray-600 text-center text-lg">
              Your AI-powered personal journal
            </Text>
          </View>

          {/* Welcome Message */}
          <View className="mb-8">
            <Text className="text-xl font-semibold text-gray-800 mb-3">
              Welcome! Let's get started
            </Text>
            <Text className="text-gray-600 leading-6">
              HabitStory uses AI to analyze your daily journal entries and create personalized weekly summaries. 
              To get started, we need your name and an OpenAI API key.
            </Text>
          </View>

          {/* Form */}
          <View className="mb-8">
            <Text className="text-gray-700 font-medium mb-2">Your Name</Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-4 text-base mb-4 bg-white"
              placeholder="Enter your name"
              value={name}
              onChangeText={setName}
              autoCapitalize="words"
              autoCorrect={false}
            />

            <Text className="text-gray-700 font-medium mb-2">OpenAI API Key</Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-4 text-base mb-3 bg-white"
              placeholder="sk-..."
              value={apiKey}
              onChangeText={setApiKey}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
            />
            
            <Text className="text-gray-500 text-sm mb-6">
              Your API key is stored securely on your device and never shared.
            </Text>
          </View>

          {/* API Key Help */}
          <View className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-8">
            <Text className="text-blue-800 font-semibold mb-2">
              How to get your OpenAI API Key:
            </Text>
            <Text className="text-blue-700 text-sm leading-5">
              1. Visit platform.openai.com{'\n'}
              2. Sign in or create an account{'\n'}
              3. Go to API Keys section{'\n'}
              4. Create a new secret key{'\n'}
              5. Copy and paste it above
            </Text>
          </View>

          {/* Login Button */}
          <TouchableOpacity
            className={`rounded-lg py-4 px-6 ${
              isLoading || !name.trim() || !apiKey.trim()
                ? 'bg-gray-300'
                : 'bg-blue-500'
            }`}
            onPress={handleLogin}
            disabled={isLoading || !name.trim() || !apiKey.trim()}
          >
            {isLoading ? (
              <View className="flex-row items-center justify-center">
                <ActivityIndicator color="white" size="small" />
                <Text className="text-white font-semibold ml-2">Setting up...</Text>
              </View>
            ) : (
              <Text className="text-white font-semibold text-center text-lg">
                Get Started
              </Text>
            )}
          </TouchableOpacity>

          {/* Privacy Note */}
          <View className="mt-8 p-4 bg-gray-50 rounded-lg">
            <Text className="text-gray-600 text-sm text-center leading-5">
              🔒 Your data stays on your device. We don't collect or store your journal entries or API key.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
