import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../context/AuthContext';


const LoginScreen: React.FC = () => {
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const { login } = useAuth();

  const handleLogin = async () => {
    if (!name.trim()) {
      Alert.alert('Missing Name', 'Please enter your name.');
      return;
    }

    setIsLoading(true);

    try {
      await login(name.trim());
    } catch (error) {
      Alert.alert(
        'Login Failed',
        error instanceof Error ? error.message : 'Failed to save your information. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView className="flex-1 bg-gradient-to-br from-blue-50 to-purple-50">
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1 px-6 py-8">
          {/* Header */}
          <View className="items-center mb-12">
            <Text className="text-4xl mb-4">📖</Text>
            <Text className="text-3xl font-bold text-gray-800 mb-2">
              HabitStory
            </Text>
            <Text className="text-gray-600 text-center text-lg">
              AI-powered insights for your personal growth
            </Text>
          </View>

          {/* Welcome Message */}
          <View className="mb-8">
            <Text className="text-xl font-semibold text-gray-800 mb-3">
              Welcome! Let's get started
            </Text>
            <Text className="text-gray-600 leading-6">
              HabitStory uses AI to analyze your daily journal entries and create personalized weekly summaries.
              Just enter your name to begin your journey of self-discovery.
            </Text>
          </View>

          {/* Form */}
          <View className="mb-8">
            <Text className="text-gray-700 font-medium mb-2">Your Name</Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-4 text-base mb-6 bg-white"
              placeholder="Enter your name"
              value={name}
              onChangeText={setName}
              autoCapitalize="words"
              autoCorrect={false}
            />

            <View className="bg-green-50 p-4 rounded-lg border border-green-200 mb-6">
              <Text className="text-green-800 font-semibold mb-2">
                ✅ AI Features Included
              </Text>
              <Text className="text-green-700 text-sm leading-5">
                Your journal entries will be automatically analyzed using advanced AI to provide personalized insights and weekly summaries.
              </Text>
            </View>
          </View>



          {/* Login Button */}
          <TouchableOpacity
            className={`rounded-lg py-4 px-6 ${
              isLoading || !name.trim()
                ? 'bg-gray-300'
                : 'bg-blue-500'
            }`}
            onPress={handleLogin}
            disabled={isLoading || !name.trim()}
          >
            {isLoading ? (
              <View className="flex-row items-center justify-center">
                <ActivityIndicator color="white" size="small" />
                <Text className="text-white font-semibold ml-2">Setting up...</Text>
              </View>
            ) : (
              <Text className="text-white font-semibold text-center text-lg">
                Get Started
              </Text>
            )}
          </TouchableOpacity>

          {/* Privacy Note */}
          <View className="mt-8 p-4 bg-gray-50 rounded-lg">
            <Text className="text-gray-600 text-sm text-center leading-5">
              🔒 Your data stays on your device. We don't collect or store your journal entries or API key.
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
