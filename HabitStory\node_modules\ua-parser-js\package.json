{"title": "UAParser.js", "name": "ua-parser-js", "version": "0.7.40", "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (http://faisalman.com)", "description": "Detect Browser, Engine, OS, CPU, and Device type/model from User-Agent data. Supports browser & node.js environment", "keywords": ["ua-parser-js", "user-agent", "browser", "engine", "os", "device", "mobile", "cpu", "platform", "detect", "javascript", "j<PERSON>y", "typescript", "node-js", "client-hints"], "homepage": "https://uaparser.dev", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "Admas <<EMAIL>>", "algenon <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON>u <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <arun<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "Bendeguz <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "boneyao <<EMAIL>>", "<PERSON> <carlchrist<PERSON><PERSON><PERSON>@gmail.com>", "CESAR RAMOS <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <d.vladim<PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "ddivernois <<EMAIL>>", "Deliaz <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "dhoko <<EMAIL>>", "dianhe <<EMAIL>>", "dineshks1 <dineshks1@<EMAIL>>", "<PERSON> <d<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "gulpin <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "hr6r <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <i.kamal<PERSON>@adguard.com>", "insanehong <<EMAIL>>", "jackpoll <<EMAIL>>", "<PERSON> <<EMAIL>>", "J<PERSON><PERSON><PERSON> <12983479+JB<PERSON><PERSON>@users.noreply.github.com>", "<PERSON> <joey<PERSON><PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <joshua<PERSON><PERSON><PERSON>@outlook.com>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> Treveil <<EMAIL>>", "leonardo <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Lithin <<EMAIL>>", "ll-syber <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Malash <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <masa<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Max Maurer <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "MimyyK <<EMAIL>>", "naoh <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Nik <PERSON> <<EMAIL>>", "nionata <<EMAIL>>", "niris <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "o.drapeza <<EMAIL>>", "otakuSiD <<EMAIL>>", "patrick-nurt <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> Hello <<PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>", "philippsimon <<EMAIL>>", "<PERSON> <<EMAIL>>", "Piper Chester <<EMAIL>>", "Queen <PERSON><PERSON>ch <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "roman.savarin <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "ruicong <<EMAIL>>", "<PERSON><PERSON> Sonntag <<EMAIL>>", "sgautrea <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "St<PERSON>el <<EMAIL>>", "sUP <<EMAIL>>", "Sylva<PERSON> <<EMAIL>>", "szchenghuang <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "Y<PERSON>lmaz <<EMAIL>>", "yuanyang <<EMAIL>>", "<PERSON>-jin <<EMAIL>>", "<PERSON> <<EMAIL>>"], "main": "src/ua-parser.js", "files": ["dist", "src"], "bin": "./script/cli.js", "scripts": {"build": "uglifyjs src/ua-parser.js -o dist/ua-parser.min.js --comments '/^ UA/' && uglifyjs src/ua-parser.js -o dist/ua-parser.pack.js --comments '/^ UA/' --compress --mangle", "test": "jshint src/ua-parser.js && mocha -R nyan test/test.js", "test-ci": "jshint src/ua-parser.js && mocha -R spec test/test.js", "verup": "node ./node_modules/verup", "version": "node ./node_modules/verup 0"}, "verup": {"files": ["bower.json", "package.js", "src/ua-parser.js"], "regs": ["^((?:\\$|(\\s*\\*\\s*@)|(\\s*(?:var|,)?\\s+))(?:LIBVERSION|version)[\\s\\:='\"]+)([0-9]+(?:\\.[0-9]+){2,2})", "^(\\/?\\s?\\*.*v)([0-9]+(?:\\.[0-9]+){2,2})"]}, "devDependencies": {"@babel/parser": "7.15.8", "@babel/traverse": "7.15.4", "jshint": "~2.12.0", "mocha": "~8.2.0", "requirejs": "^2.3.2", "safe-regex": "^2.1.1", "uglify-js": "~3.12.0", "verup": "^1.3.x"}, "repository": {"type": "git", "url": "https://github.com/faisalman/ua-parser-js.git"}, "license": "MIT", "engines": {"node": "*"}, "directories": {"dist": "dist", "src": "src", "test": "test"}, "bugs": "https://github.com/faisalman/ua-parser-js/issues", "demo": "https://uaparser.dev", "download": "https://raw.github.com/faisalman/ua-parser-js/master/dist/ua-parser.min.js", "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}]}