import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useEntries } from '../hooks/useEntries';
import { Entry } from '../lib/db';
import '../lib/global.css';

const HistoryScreen: React.FC = () => {
  const { entries, isLoading, error, refreshEntries, removeEntry } = useEntries();
  const [selectedEntry, setSelectedEntry] = useState<Entry | null>(null);

  useEffect(() => {
    refreshEntries();
  }, []);

  const handleDeleteEntry = (entry: Entry) => {
    Alert.alert(
      'Delete Entry',
      `Are you sure you want to delete the entry from ${entry.date}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await removeEntry(entry.id);
              if (selectedEntry?.id === entry.id) {
                setSelectedEntry(null);
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to delete entry');
            }
          },
        },
      ]
    );
  };

  const renderEntryCard = (entry: Entry) => {
    const habits = JSON.parse(entry.habits || '[]');
    const metrics = JSON.parse(entry.metrics || '{}');
    const date = new Date(entry.date);
    const isSelected = selectedEntry?.id === entry.id;

    return (
      <TouchableOpacity
        key={entry.id}
        className={`mb-4 p-4 rounded-lg border ${
          isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'
        }`}
        onPress={() => setSelectedEntry(isSelected ? null : entry)}
      >
        <View className="flex-row justify-between items-start mb-2">
          <Text className="text-lg font-semibold text-gray-800">
            {date.toLocaleDateString('en-US', { 
              weekday: 'short', 
              month: 'short', 
              day: 'numeric' 
            })}
          </Text>
          <TouchableOpacity
            onPress={() => handleDeleteEntry(entry)}
            className="p-2"
          >
            <Text className="text-red-500 font-medium">Delete</Text>
          </TouchableOpacity>
        </View>

        <Text className="text-gray-600 mb-3" numberOfLines={isSelected ? undefined : 3}>
          {entry.text}
        </Text>

        {isSelected && (
          <View className="border-t border-gray-200 pt-3">
            {habits.length > 0 && (
              <View className="mb-3">
                <Text className="font-medium text-gray-700 mb-1">Habits:</Text>
                <View className="flex-row flex-wrap">
                  {habits.map((habit: string, index: number) => (
                    <View key={index} className="bg-green-100 px-2 py-1 rounded-full mr-2 mb-1">
                      <Text className="text-green-700 text-sm">{habit}</Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {Object.keys(metrics).length > 0 && (
              <View className="mb-3">
                <Text className="font-medium text-gray-700 mb-1">Metrics:</Text>
                <View className="flex-row flex-wrap">
                  {Object.entries(metrics).map(([key, value]) => (
                    <View key={key} className="bg-blue-100 px-2 py-1 rounded-full mr-2 mb-1">
                      <Text className="text-blue-700 text-sm">
                        {key.replace('_', ' ')}: {value}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {entry.reflection && (
              <View>
                <Text className="font-medium text-gray-700 mb-1">Reflection:</Text>
                <Text className="text-gray-600 italic">"{entry.reflection}"</Text>
              </View>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <View className="px-4 py-2">
        <Text className="text-2xl font-bold text-gray-800 mb-2">
          Journal History
        </Text>
        <Text className="text-gray-600 mb-4">
          {entries.length} {entries.length === 1 ? 'entry' : 'entries'} recorded
        </Text>
      </View>

      {error && (
        <View className="mx-4 mb-4 bg-red-50 border border-red-200 rounded-lg p-4">
          <Text className="text-red-800 font-medium">Error:</Text>
          <Text className="text-red-700">{error}</Text>
        </View>
      )}

      <ScrollView
        className="flex-1 px-4"
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={refreshEntries} />
        }
      >
        {entries.length === 0 ? (
          <View className="flex-1 justify-center items-center py-20">
            <Text className="text-gray-500 text-lg text-center">
              No journal entries yet.{'\n'}Start writing to see your history here!
            </Text>
          </View>
        ) : (
          entries.map(renderEntryCard)
        )}
        
        <View className="h-8" />
      </ScrollView>
    </SafeAreaView>
  );
};

export default HistoryScreen;
