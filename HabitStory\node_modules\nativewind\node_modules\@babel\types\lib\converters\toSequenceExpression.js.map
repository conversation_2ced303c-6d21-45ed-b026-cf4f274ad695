{"version": 3, "names": ["toSequenceExpression", "nodes", "scope", "length", "declars", "result", "gatherSequenceExpressions", "declar", "push"], "sources": ["../../src/converters/toSequenceExpression.ts"], "sourcesContent": ["import gatherSequenceExpressions from \"./gatherSequenceExpressions\";\nimport type * as t from \"..\";\nimport type { DeclarationInfo } from \"./gatherSequenceExpressions\";\n\n/**\n * Turn an array of statement `nodes` into a `SequenceExpression`.\n *\n * Variable declarations are turned into simple assignments and their\n * declarations hoisted to the top of the current scope.\n *\n * Expression statements are just resolved to their expression.\n */\nexport default function toSequenceExpression(\n  nodes: ReadonlyArray<t.Node>,\n  scope: any,\n): t.SequenceExpression | undefined {\n  if (!nodes?.length) return;\n\n  const declars: DeclarationInfo[] = [];\n  const result = gatherSequenceExpressions(nodes, scope, declars);\n  if (!result) return;\n\n  for (const declar of declars) {\n    scope.push(declar);\n  }\n\n  // @ts-expect-error fixme: gatherSequenceExpressions will return an Expression when there are only one element\n  return result;\n}\n"], "mappings": ";;;;;;;AAAA;;AAYe,SAASA,oBAAT,CACbC,KADa,EAEbC,KAFa,EAGqB;EAClC,IAAI,EAACD,KAAD,YAACA,KAAK,CAAEE,MAAR,CAAJ,EAAoB;EAEpB,MAAMC,OAA0B,GAAG,EAAnC;EACA,MAAMC,MAAM,GAAG,IAAAC,kCAAA,EAA0BL,KAA1B,EAAiCC,KAAjC,EAAwCE,OAAxC,CAAf;EACA,IAAI,CAACC,MAAL,EAAa;;EAEb,KAAK,MAAME,MAAX,IAAqBH,OAArB,EAA8B;IAC5BF,KAAK,CAACM,IAAN,CAAWD,MAAX;EACD;;EAGD,OAAOF,MAAP;AACD"}