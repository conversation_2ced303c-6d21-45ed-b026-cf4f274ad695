{"version": 3, "names": ["isBlockScoped", "node", "isFunctionDeclaration", "isClassDeclaration", "isLet"], "sources": ["../../src/validators/isBlockScoped.ts"], "sourcesContent": ["import { isClassDeclaration, isFunctionDeclaration } from \"./generated\";\nimport isLet from \"./isLet\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `node` is block scoped.\n */\nexport default function isBlockScoped(node: t.Node): boolean {\n  return isFunctionDeclaration(node) || isClassDeclaration(node) || isLet(node);\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAMe,SAASA,aAAT,CAAuBC,IAAvB,EAA8C;EAC3D,OAAO,IAAAC,gCAAA,EAAsBD,IAAtB,KAA+B,IAAAE,6BAAA,EAAmBF,IAAnB,CAA/B,IAA2D,IAAAG,cAAA,EAAMH,IAAN,CAAlE;AACD"}