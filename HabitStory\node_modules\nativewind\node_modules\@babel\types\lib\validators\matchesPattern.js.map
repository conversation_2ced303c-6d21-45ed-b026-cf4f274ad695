{"version": 3, "names": ["matchesPattern", "member", "match", "allowPartial", "isMemberExpression", "parts", "Array", "isArray", "split", "nodes", "node", "object", "push", "property", "length", "i", "j", "value", "isIdentifier", "name", "isStringLiteral", "isThisExpression"], "sources": ["../../src/validators/matchesPattern.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isMemberExpression,\n  isStringLiteral,\n  isThisExpression,\n} from \"./generated\";\nimport type * as t from \"..\";\n\n/**\n * Determines whether or not the input node `member` matches the\n * input `match`.\n *\n * For example, given the match `React.createClass` it would match the\n * parsed nodes of `React.createClass` and `React[\"createClass\"]`.\n */\nexport default function matchesPattern(\n  member: t.Node | null | undefined,\n  match: string | string[],\n  allowPartial?: boolean,\n): boolean {\n  // not a member expression\n  if (!isMemberExpression(member)) return false;\n\n  const parts = Array.isArray(match) ? match : match.split(\".\");\n  const nodes = [];\n\n  let node;\n  for (node = member; isMemberExpression(node); node = node.object) {\n    nodes.push(node.property);\n  }\n  nodes.push(node);\n\n  if (nodes.length < parts.length) return false;\n  if (!allowPartial && nodes.length > parts.length) return false;\n\n  for (let i = 0, j = nodes.length - 1; i < parts.length; i++, j--) {\n    const node = nodes[j];\n    let value;\n    if (isIdentifier(node)) {\n      value = node.name;\n    } else if (isStringLiteral(node)) {\n      value = node.value;\n    } else if (isThisExpression(node)) {\n      value = \"this\";\n    } else {\n      return false;\n    }\n\n    if (parts[i] !== value) return false;\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;;AAAA;;AAee,SAASA,cAAT,CACbC,MADa,EAEbC,KAFa,EAGbC,YAHa,EAIJ;EAET,IAAI,CAAC,IAAAC,6BAAA,EAAmBH,MAAnB,CAAL,EAAiC,OAAO,KAAP;EAEjC,MAAMI,KAAK,GAAGC,KAAK,CAACC,OAAN,CAAcL,KAAd,IAAuBA,KAAvB,GAA+BA,KAAK,CAACM,KAAN,CAAY,GAAZ,CAA7C;EACA,MAAMC,KAAK,GAAG,EAAd;EAEA,IAAIC,IAAJ;;EACA,KAAKA,IAAI,GAAGT,MAAZ,EAAoB,IAAAG,6BAAA,EAAmBM,IAAnB,CAApB,EAA8CA,IAAI,GAAGA,IAAI,CAACC,MAA1D,EAAkE;IAChEF,KAAK,CAACG,IAAN,CAAWF,IAAI,CAACG,QAAhB;EACD;;EACDJ,KAAK,CAACG,IAAN,CAAWF,IAAX;EAEA,IAAID,KAAK,CAACK,MAAN,GAAeT,KAAK,CAACS,MAAzB,EAAiC,OAAO,KAAP;EACjC,IAAI,CAACX,YAAD,IAAiBM,KAAK,CAACK,MAAN,GAAeT,KAAK,CAACS,MAA1C,EAAkD,OAAO,KAAP;;EAElD,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,CAAC,GAAGP,KAAK,CAACK,MAAN,GAAe,CAAnC,EAAsCC,CAAC,GAAGV,KAAK,CAACS,MAAhD,EAAwDC,CAAC,IAAIC,CAAC,EAA9D,EAAkE;IAChE,MAAMN,IAAI,GAAGD,KAAK,CAACO,CAAD,CAAlB;IACA,IAAIC,KAAJ;;IACA,IAAI,IAAAC,uBAAA,EAAaR,IAAb,CAAJ,EAAwB;MACtBO,KAAK,GAAGP,IAAI,CAACS,IAAb;IACD,CAFD,MAEO,IAAI,IAAAC,0BAAA,EAAgBV,IAAhB,CAAJ,EAA2B;MAChCO,KAAK,GAAGP,IAAI,CAACO,KAAb;IACD,CAFM,MAEA,IAAI,IAAAI,2BAAA,EAAiBX,IAAjB,CAAJ,EAA4B;MACjCO,KAAK,GAAG,MAAR;IACD,CAFM,MAEA;MACL,OAAO,KAAP;IACD;;IAED,IAAIZ,KAAK,CAACU,CAAD,CAAL,KAAaE,KAAjB,EAAwB,OAAO,KAAP;EACzB;;EAED,OAAO,IAAP;AACD"}