import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { initDatabase } from '../lib/db';
import { initializeNotifications, setupNotificationListeners } from '../lib/notifications';
import { AuthProvider } from './AuthContext';

interface AppContextType {
  isInitialized: boolean;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize database
        await initDatabase();

        // Initialize notifications
        await initializeNotifications();

        console.log('App initialized successfully');
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        // You might want to show an error screen here
        setIsInitialized(true); // Still set to true to prevent infinite loading
      }
    };

    initializeApp();

    // Set up notification listeners
    const cleanupNotifications = setupNotificationListeners();

    // Cleanup function
    return () => {
      cleanupNotifications();
    };
  }, []);

  const value: AppContextType = {
    isInitialized,
  };

  return (
    <AppContext.Provider value={value}>
      <AuthProvider>
        {children}
      </AuthProvider>
    </AppContext.Provider>
  );
};

export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
