#!/usr/bin/env node

/**
 * HabitStory Parse Test Script
 * 
 * This script tests the OpenAI parsing functionality by sending a sample
 * journal entry to GPT-4 and displaying the extracted habits, metrics,
 * and reflection.
 * 
 * Usage:
 *   node scripts/parse-test.js "Your journal entry text here"
 *   node scripts/parse-test.js --interactive
 */

const axios = require('axios');
const readline = require('readline');

// Sample journal entries for testing
const SAMPLE_ENTRIES = [
  "Today I woke up at 7 AM, went for a 30-minute run, had a healthy breakfast, and felt really productive at work. I meditated for 10 minutes before bed and felt grateful for the day.",
  
  "Had a rough day today. Woke up late, skipped breakfast, and felt anxious about the presentation. Managed to get through it though. Ordered pizza for dinner and watched Netflix until midnight.",
  
  "Great day! Started with yoga at 6:30 AM, had a green smoothie for breakfast. Work was challenging but rewarding. Cooked dinner with my partner and we talked for hours. Read for 30 minutes before bed.",
  
  "Feeling overwhelmed lately. Work has been stressful and I haven't been sleeping well. Tried to go for a walk during lunch but it started raining. Need to find better ways to manage stress.",
  
  "Weekend vibes! Slept in until 9 AM, made pancakes for breakfast. Went hiking with friends for 2 hours. Felt so connected to nature. Had a BBQ in the evening and played board games."
];

// OpenAI parsing prompt (simplified version)
const PARSE_PROMPT = `You are an AI assistant that analyzes personal journal entries to extract structured information. 

Analyze the following journal entry and extract:

1. **Habits** - Specific behaviors, routines, or activities mentioned
2. **Metrics** - Quantifiable data (times, durations, amounts, ratings)
3. **Reflection** - The person's main emotional state, insight, or takeaway
4. **User Traits** - Communication style, personality indicators, values

Return your analysis in this exact JSON format:
{
  "habits": ["habit1", "habit2", ...],
  "metrics": {
    "metric_name": "value",
    "another_metric": "value"
  },
  "reflection": "One sentence summary of emotional state or main insight",
  "user_traits": {
    "tone": "optimistic/neutral/pessimistic",
    "style": "detailed/concise/casual/formal",
    "traits": ["trait1", "trait2", ...]
  }
}

Journal Entry: "{ENTRY_TEXT}"

Analysis:`;

// Get OpenAI API key from environment or prompt user
async function getApiKey() {
  if (process.env.OPENAI_API_KEY) {
    return process.env.OPENAI_API_KEY;
  }

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question('Enter your OpenAI API key: ', (apiKey) => {
      rl.close();
      resolve(apiKey.trim());
    });
  });
}

// Parse journal entry using OpenAI
async function parseEntry(entryText, apiKey) {
  const prompt = PARSE_PROMPT.replace('{ENTRY_TEXT}', entryText);

  try {
    console.log('🤖 Sending to OpenAI...\n');

    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-4',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.7
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const content = response.data.choices[0].message.content;
    
    // Try to parse JSON from the response
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    } else {
      throw new Error('No valid JSON found in response');
    }
  } catch (error) {
    if (error.response) {
      throw new Error(`OpenAI API Error: ${error.response.status} - ${error.response.data.error?.message || 'Unknown error'}`);
    } else if (error.message.includes('JSON')) {
      throw new Error('Failed to parse OpenAI response as JSON');
    } else {
      throw new Error(`Network Error: ${error.message}`);
    }
  }
}

// Display parsed results in a nice format
function displayResults(entryText, parsed) {
  console.log('📝 JOURNAL ENTRY:');
  console.log('─'.repeat(50));
  console.log(`"${entryText}"`);
  console.log();

  console.log('🔍 ANALYSIS RESULTS:');
  console.log('─'.repeat(50));

  console.log('🏃 HABITS IDENTIFIED:');
  if (parsed.habits && parsed.habits.length > 0) {
    parsed.habits.forEach((habit, index) => {
      console.log(`  ${index + 1}. ${habit}`);
    });
  } else {
    console.log('  No specific habits identified');
  }
  console.log();

  console.log('📊 METRICS EXTRACTED:');
  if (parsed.metrics && Object.keys(parsed.metrics).length > 0) {
    Object.entries(parsed.metrics).forEach(([key, value]) => {
      console.log(`  • ${key.replace('_', ' ')}: ${value}`);
    });
  } else {
    console.log('  No quantifiable metrics found');
  }
  console.log();

  console.log('💭 REFLECTION:');
  console.log(`  "${parsed.reflection || 'No reflection extracted'}"`);
  console.log();

  console.log('👤 USER TRAITS:');
  if (parsed.user_traits) {
    console.log(`  • Tone: ${parsed.user_traits.tone || 'Unknown'}`);
    console.log(`  • Style: ${parsed.user_traits.style || 'Unknown'}`);
    if (parsed.user_traits.traits && parsed.user_traits.traits.length > 0) {
      console.log(`  • Traits: ${parsed.user_traits.traits.join(', ')}`);
    }
  }
  console.log();
}

// Interactive mode
async function interactiveMode(apiKey) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('🎯 INTERACTIVE PARSE TEST MODE');
  console.log('Enter journal entries to test parsing (type "quit" to exit)\n');

  while (true) {
    const entryText = await new Promise((resolve) => {
      rl.question('📝 Enter journal entry: ', resolve);
    });

    if (entryText.toLowerCase() === 'quit') {
      break;
    }

    if (!entryText.trim()) {
      console.log('Please enter some text.\n');
      continue;
    }

    try {
      const parsed = await parseEntry(entryText, apiKey);
      console.log();
      displayResults(entryText, parsed);
      console.log('─'.repeat(50));
    } catch (error) {
      console.error(`❌ Error: ${error.message}\n`);
    }
  }

  rl.close();
}

// Main function
async function main() {
  console.log('🧪 HabitStory Parse Test Script\n');

  try {
    const apiKey = await getApiKey();
    
    if (!apiKey || !apiKey.startsWith('sk-')) {
      console.error('❌ Invalid OpenAI API key. Must start with "sk-"');
      process.exit(1);
    }

    const args = process.argv.slice(2);
    
    if (args.includes('--interactive') || args.includes('-i')) {
      await interactiveMode(apiKey);
      return;
    }

    // Use provided text or sample entry
    let entryText = args.join(' ').trim();
    
    if (!entryText) {
      console.log('No entry provided. Using sample entry...\n');
      entryText = SAMPLE_ENTRIES[0];
    }

    const parsed = await parseEntry(entryText, apiKey);
    displayResults(entryText, parsed);

    console.log('✅ Parse test completed successfully!');
    console.log('\n💡 Try running with different entries:');
    console.log('   node scripts/parse-test.js "Your custom entry here"');
    console.log('   node scripts/parse-test.js --interactive');

  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
