{"version": 3, "names": ["isLet", "node", "isVariableDeclaration", "kind", "BLOCK_SCOPED_SYMBOL"], "sources": ["../../src/validators/isLet.ts"], "sourcesContent": ["import { isVariableDeclaration } from \"./generated\";\nimport { BLOCK_SCOPED_SYMBOL } from \"../constants\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `node` is a `let` variable declaration.\n */\nexport default function isLet(node: t.Node): boolean {\n  return (\n    isVariableDeclaration(node) &&\n    (node.kind !== \"var\" ||\n      // @ts-expect-error Fixme: document private properties\n      node[BLOCK_SCOPED_SYMBOL])\n  );\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAMe,SAASA,KAAT,CAAeC,IAAf,EAAsC;EACnD,OACE,IAAAC,gCAAA,EAAsBD,IAAtB,MACCA,IAAI,CAACE,IAAL,KAAc,KAAd,IAECF,IAAI,CAACG,8BAAD,CAHN,CADF;AAMD"}