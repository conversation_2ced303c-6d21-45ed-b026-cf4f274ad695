{"version": 3, "names": ["toBindingIdentifierName", "name", "toIdentifier"], "sources": ["../../src/converters/toBindingIdentifierName.ts"], "sourcesContent": ["import toIdentifier from \"./toIdentifier\";\n\nexport default function toBindingIdentifierName(name: string): string {\n  name = toIdentifier(name);\n  if (name === \"eval\" || name === \"arguments\") name = \"_\" + name;\n\n  return name;\n}\n"], "mappings": ";;;;;;;AAAA;;AAEe,SAASA,uBAAT,CAAiCC,IAAjC,EAAuD;EACpEA,IAAI,GAAG,IAAAC,qBAAA,EAAaD,IAAb,CAAP;EACA,IAAIA,IAAI,KAAK,MAAT,IAAmBA,IAAI,KAAK,WAAhC,EAA6CA,IAAI,GAAG,MAAMA,IAAb;EAE7C,OAAOA,IAAP;AACD"}