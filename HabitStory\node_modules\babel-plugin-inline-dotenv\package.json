{"name": "babel-plugin-inline-dotenv", "version": "1.7.0", "description": "Load your `.env` file and replace `process.env.MY_VARIABLE` with the value you set.", "main": "src/index.js", "scripts": {"test": "EXAMPLE_ENV_VAR_SYSTEM=system EXAMPLE_ENV_VAR_SECRET=secret mocha --require @babel/register", "test:watch": "npm run test -- --watch"}, "repository": {"type": "git", "url": "git+https://github.com/brysgo/babel-plugin-inline-dotenv.git"}, "keywords": ["babel", "plugin", "dotenv"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/brysgo/babel-plugin-inline-dotenv/issues"}, "homepage": "https://github.com/brysgo/babel-plugin-inline-dotenv#readme", "dependencies": {"dotenv": "^16.0.0"}, "peerDependencies": {"dotenv-expand": ">= 5.0.0"}, "peerDependenciesMeta": {"dotenv-expand": {"optional": true}}, "devDependencies": {"@babel/cli": "^7.10.5", "@babel/core": "^7.11.1", "@babel/preset-env": "^7.11.0", "@babel/register": "^7.10.5", "babel-eslint": "^10.1.0", "dotenv-expand": "^8.0.1", "mocha": "9.2.0", "prettier": "^2.0.5"}}