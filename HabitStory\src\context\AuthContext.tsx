import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import * as SecureStore from 'expo-secure-store';
import { setApiKey, getApiKey, removeApi<PERSON>ey, hasApi<PERSON>ey } from '../lib/gemini';

interface User {
  name: string;
  hasApiKey: boolean;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (name: string) => Promise<void>;
  logout: () => Promise<void>;
  updateApiKey: (apiKey: string) => Promise<void>;
  updateUserName: (name: string) => Promise<void>;
  checkAuthStatus: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const USER_NAME_KEY = 'user_name';

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuthStatus = async () => {
    setIsLoading(true);
    try {
      const [userName, apiKeyExists] = await Promise.all([
        SecureStore.getItemAsync(USER_NAME_KEY),
        hasApiKey()
      ]);

      if (userName && apiKeyExists) {
        setUser({
          name: userName,
          hasApiKey: true
        });
      } else if (userName) {
        setUser({
          name: userName,
          hasApiKey: false
        });
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (name: string) => {
    setIsLoading(true);
    try {
      // Store user name
      await SecureStore.setItemAsync(USER_NAME_KEY, name);

      // Check if centralized API key is available
      const apiKeyAvailable = await hasApiKey();

      setUser({
        name,
        hasApiKey: apiKeyAvailable
      });
    } catch (error) {
      console.error('Error during login:', error);
      throw new Error('Failed to save user information');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      // Remove user name only (API key is centralized)
      await SecureStore.deleteItemAsync(USER_NAME_KEY);

      setUser(null);
    } catch (error) {
      console.error('Error during logout:', error);
      throw new Error('Failed to clear user information');
    } finally {
      setIsLoading(false);
    }
  };

  const updateApiKey = async (apiKey: string) => {
    // API key is centralized, no user-specific updates needed
    console.log('API key is centralized - no user update required');

    if (user) {
      // Check if centralized API key is available
      const apiKeyAvailable = await hasApiKey();
      setUser({
        ...user,
        hasApiKey: apiKeyAvailable
      });
    }
  };

  const updateUserName = async (name: string) => {
    try {
      await SecureStore.setItemAsync(USER_NAME_KEY, name);
      
      if (user) {
        setUser({
          ...user,
          name
        });
      }
    } catch (error) {
      console.error('Error updating user name:', error);
      throw new Error('Failed to update user name');
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: user !== null && user.hasApiKey,
    login,
    logout,
    updateApiKey,
    updateUserName,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
