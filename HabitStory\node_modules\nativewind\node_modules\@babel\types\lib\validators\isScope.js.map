{"version": 3, "names": ["isScope", "node", "parent", "isBlockStatement", "isFunction", "isCatchClause", "isPattern", "isScopable"], "sources": ["../../src/validators/isScope.ts"], "sourcesContent": ["import {\n  isFunction,\n  isCatchClause,\n  isBlockStatement,\n  isScopable,\n  isPattern,\n} from \"./generated\";\nimport type * as t from \"..\";\n\n/**\n * Check if the input `node` is a scope.\n */\nexport default function isScope(node: t.Node, parent: t.Node): boolean {\n  // If a BlockStatement is an immediate descendent of a Function/CatchClause, it must be in the body.\n  // Hence we skipped the parentKey === \"params\" check\n  if (isBlockStatement(node) && (isFunction(parent) || isCatchClause(parent))) {\n    return false;\n  }\n\n  // If a Pattern is an immediate descendent of a Function/CatchClause, it must be in the params.\n  // Hence we skipped the parentKey === \"params\" check\n  if (isPattern(node) && (isFunction(parent) || isCatchClause(parent))) {\n    return true;\n  }\n\n  return isScopable(node);\n}\n"], "mappings": ";;;;;;;AAAA;;AAYe,SAASA,OAAT,CAAiBC,IAAjB,EAA+BC,MAA/B,EAAwD;EAGrE,IAAI,IAAAC,2BAAA,EAAiBF,IAAjB,MAA2B,IAAAG,qBAAA,EAAWF,MAAX,KAAsB,IAAAG,wBAAA,EAAcH,MAAd,CAAjD,CAAJ,EAA6E;IAC3E,OAAO,KAAP;EACD;;EAID,IAAI,IAAAI,oBAAA,EAAUL,IAAV,MAAoB,IAAAG,qBAAA,EAAWF,MAAX,KAAsB,IAAAG,wBAAA,EAAcH,MAAd,CAA1C,CAAJ,EAAsE;IACpE,OAAO,IAAP;EACD;;EAED,OAAO,IAAAK,qBAAA,EAAWN,IAAX,CAAP;AACD"}