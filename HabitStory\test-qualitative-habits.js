// Test the new qualitative habits functionality

const axios = require('axios');

// Gemini API configuration
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro:generateContent';
const API_KEY = 'AIzaSyBPGdbmlVQzY3XsVEXo_UPElTtVBiNSR_4';

// Test journal entry with qualitative habits
const TEST_ENTRY = `Hoy fue un día increíble! Me desperté temprano a las 6:30 AM y comí un desayuno muy saludable con avena y frutas. Hice ejercicio por 45 minutos en el gimnasio y me sentí súper energizado. 

En el trabajo logré completar todas mis tareas importantes y me mantuve muy concentrado sin distracciones. Almorcé en casa algo casero y nutritivo.

Por la tarde llamé a mi mamá y tuvimos una conversación muy linda de 30 minutos. También leí por 20 minutos antes de dormir y practiqué meditación por 10 minutos.

Me siento muy agradecido por este día productivo y saludable. Dormí 8 horas y mi nivel de estrés fue muy bajo, como un 2/10.`;

const prompt = `You are an AI assistant that analyzes daily journal entries to extract structured data. 

Analyze the following journal entry and extract:

1. **habits**: Array of habit-related activities mentioned (e.g., ["morning run", "meditation", "reading"]) - KEEP FOR COMPATIBILITY
2. **qualitative_habits**: Array of qualitative habits with detailed tracking:
   Each habit should be an object with:
   - name: Clear, consistent habit name (e.g., "ate healthy", "slept well", "exercised", "meditated", "read", "socialized")
   - category: One of "health", "productivity", "wellness", "social", "personal"
   - completed: true if the habit was done/achieved, false if mentioned as not done
   - confidence: 0-1 score of how confident you are about this detection
   
   Examples of qualitative habits to detect:
   - Health: "ate healthy", "drank water", "took vitamins", "avoided junk food", "cooked at home"
   - Productivity: "worked focused", "completed tasks", "organized space", "planned day", "avoided distractions"
   - Wellness: "slept well", "felt energized", "managed stress", "practiced gratitude", "relaxed"
   - Social: "connected with friends", "called family", "helped someone", "attended social event"
   - Personal: "read", "learned something", "practiced hobby", "reflected", "journaled"

3. **metrics**: Object with quantifiable data when mentioned:
   - sleep_hours: Hours of sleep (number)
   - water_liters: Water consumption in liters (number)
   - exercise_minutes: Exercise duration in minutes (number)
   - mood_score: Mood on scale 1-10 (number)
   - stress_level: Stress on scale 1-10 (number)
   - productivity_score: Productivity on scale 1-10 (number)
   - social_interactions: Number of meaningful social interactions (number)
   - screen_time_hours: Screen time in hours (number)
   - meditation_minutes: Meditation time in minutes (number)
   - steps: Number of steps taken (number)
   - Any other quantifiable metrics mentioned
4. **reflection**: A brief 1-2 sentence summary of the main insight or feeling from the entry
5. **user_traits**: Object analyzing the user's communication style:
   - tone: "formal", "informal", "casual", "professional"
   - style: "descriptive", "concise", "emotional", "analytical", "conversational", "poetic"
   - traits: Object with personality traits like {"optimistic": true, "detail_oriented": false, "introspective": true}

Previous user traits for reference: None

Journal Entry:
"${TEST_ENTRY}"

IMPORTANT: Respond with ONLY a valid JSON object. Do not include any explanatory text, markdown formatting, or code blocks. Just the raw JSON object in this exact format:

{
  "habits": ["habit1", "habit2"],
  "qualitative_habits": [
    {
      "name": "ate healthy",
      "category": "health",
      "completed": true,
      "confidence": 0.9
    },
    {
      "name": "slept well",
      "category": "wellness",
      "completed": false,
      "confidence": 0.8
    }
  ],
  "metrics": {"sleep_hours": 8, "mood_score": 7},
  "reflection": "Brief insight from the entry",
  "user_traits": {
    "tone": "informal",
    "style": "conversational",
    "traits": {"optimistic": true, "detail_oriented": false}
  }
}`;

async function testQualitativeHabits() {
  try {
    console.log('Testing qualitative habits detection...\n');
    console.log('Journal Entry:');
    console.log(TEST_ENTRY);
    console.log('\n' + '='.repeat(50) + '\n');

    const response = await axios.post(
      `${GEMINI_API_URL}?key=${API_KEY}`,
      {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        }
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      }
    );

    if (response.data?.candidates?.[0]?.content?.parts?.[0]?.text) {
      let rawResponse = response.data.candidates[0].content.parts[0].text.trim();
      console.log('Raw Gemini Response:');
      console.log(rawResponse);
      console.log('\n' + '='.repeat(50) + '\n');

      // Clean the response
      let jsonString = rawResponse;
      jsonString = jsonString.replace(/```json\s*/g, '').replace(/```\s*$/g, '');
      const jsonMatch = jsonString.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonString = jsonMatch[0];
      }

      // Fix common JSON issues
      // Remove trailing commas before closing brackets/braces
      jsonString = jsonString.replace(/,(\s*[}\]])/g, '$1');
      // Remove extra commas
      jsonString = jsonString.replace(/,,+/g, ',');

      const parsed = JSON.parse(jsonString);
      
      console.log('Parsed Results:');
      console.log('===============');
      console.log('Traditional Habits:', parsed.habits);
      console.log('\nQualitative Habits:');
      parsed.qualitative_habits?.forEach((habit, index) => {
        console.log(`  ${index + 1}. ${habit.name} (${habit.category}) - ${habit.completed ? '✅' : '❌'} - Confidence: ${habit.confidence}`);
      });
      console.log('\nMetrics:', parsed.metrics);
      console.log('\nReflection:', parsed.reflection);
      console.log('\nUser Traits:', parsed.user_traits);

      console.log('\n' + '='.repeat(50));
      console.log('✅ Test completed successfully!');
      console.log(`Found ${parsed.qualitative_habits?.length || 0} qualitative habits`);
      
    } else {
      console.log('❌ Invalid response format from Gemini API');
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testQualitativeHabits();
