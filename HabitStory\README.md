# HabitStory 📖

**Your AI-powered personal journal that transforms daily reflections into personalized insights.**

HabitStory is a mobile application built with Expo + React Native that uses GPT-4 to analyze your daily journal entries and generate hyper-personalized weekly summaries. All data stays on your device, ensuring complete privacy.

## ✨ Features

- **Daily Journaling**: Write free-form entries about your day
- **AI Analysis**: GPT-4 extracts habits, metrics, and insights from your text
- **Weekly Summaries**: Personalized HTML reports with trends and recommendations
- **Local Storage**: All data stored securely on your device using SQLite
- **Smart Notifications**: Daily reminders and weekly summary alerts
- **User Feedback**: Rate and improve AI-generated reports
- **Privacy First**: No external servers, your data never leaves your device

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- Expo CLI (`npm install -g @expo/cli`)
- OpenAI API key ([Get one here](https://platform.openai.com/api-keys))

### Installation

1. **Clone and setup**
   ```bash
   git clone <repository-url>
   cd HabitStory
   npm install
   ```

2. **Start the development server**
   ```bash
   npx expo start
   ```

3. **Run on device**
   - Install Expo Go app on your phone
   - Scan the QR code from the terminal
   - Or use an emulator: `npx expo start --android` or `npx expo start --ios`

### First Time Setup

1. Enter your name when prompted
2. Add your OpenAI API key (stored securely on device)
3. Grant notification permissions for reminders
4. Start journaling!

## 📱 How to Use

### Daily Journaling
1. Open the **Journal** tab
2. Write about your day in free-form text
3. Tap **"Analyze Entry"** to extract insights
4. View identified habits, metrics, and reflections

### Weekly Reports
1. Go to the **Reports** tab
2. Tap **"Generate Weekly Summary"** (requires at least one entry)
3. View your personalized HTML report
4. Provide feedback to improve future reports

### History & Settings
- **History**: Browse past entries and their analysis
- **Settings**: Update your name, API key, or logout

## 🔧 Technical Architecture

### Core Technologies
- **Frontend**: React Native + Expo
- **Styling**: NativeWind (Tailwind CSS for React Native)
- **Database**: SQLite (expo-sqlite)
- **AI Integration**: OpenAI GPT-4 API
- **Navigation**: React Navigation v6
- **Notifications**: Expo Notifications
- **Security**: Expo SecureStore for API key storage

### Database Schema
```sql
-- Journal entries
entries (id, date, text, habits, metrics, reflection, created_at)

-- Weekly reports
reports (id, week_start, week_end, html_content, feedback_rating, feedback_text, created_at)

-- User personality traits
user_traits (id, tone, style, traits, updated_at)

-- Report feedback
feedback (id, report_id, rating, text, created_at)
```

### Project Structure
```
src/
├── components/          # Reusable UI components
│   ├── AppNavigator.tsx # Main navigation setup
│   └── TabNavigator.tsx # Bottom tab navigation
├── screens/            # App screens
│   ├── JournalScreen.tsx
│   ├── HistoryScreen.tsx
│   ├── ReportScreen.tsx
│   ├── ReportWebViewScreen.tsx
│   ├── SettingsScreen.tsx
│   ├── LoginScreen.tsx
│   └── LoadingScreen.tsx
├── hooks/              # Custom React hooks
│   ├── useOpenAI.ts    # OpenAI API integration
│   ├── useEntries.ts   # Journal entries management
│   └── useReports.ts   # Reports management
├── context/            # React Context providers
│   ├── AuthContext.tsx # User authentication
│   └── AppContext.tsx  # App initialization
├── lib/                # Core utilities
│   ├── db.ts          # SQLite database layer
│   ├── openai.ts      # OpenAI API client
│   ├── prompts.ts     # GPT-4 prompt templates
│   ├── notifications.ts # Push notifications
│   └── global.css     # NativeWind styles
```

## 🧪 Testing

### Parse Test Script
Test the AI parsing functionality:

```bash
# Create a test entry
node scripts/parse-test.js "Today I woke up at 7 AM, went for a 30-minute run, had a healthy breakfast, and felt really productive at work. I meditated for 10 minutes before bed and felt grateful for the day."
```

### Manual Testing Checklist
- [ ] User onboarding flow
- [ ] Journal entry creation and analysis
- [ ] Weekly report generation
- [ ] Notification scheduling
- [ ] Data persistence across app restarts
- [ ] API key validation
- [ ] Error handling for network issues

## 🔐 Privacy & Security

- **Local-First**: All journal data stored on device using SQLite
- **Secure Storage**: API keys encrypted using Expo SecureStore
- **No Tracking**: No analytics, telemetry, or user tracking
- **OpenAI API**: Only journal text sent for analysis (no personal info)
- **Offline Capable**: Core functionality works without internet

## 📋 Configuration

### Notification Settings
- **Daily Reminder**: 9:00 PM (customizable)
- **Weekly Summary**: Sunday 10:00 AM (customizable)

### OpenAI Settings
- **Model**: GPT-4 (configurable in `src/lib/openai.ts`)
- **Max Tokens**: 2000 for parsing, 4000 for summaries
- **Temperature**: 0.7 for creative but consistent responses

## 🚨 Troubleshooting

### Common Issues

**"API key not working"**
- Ensure your OpenAI API key starts with `sk-`
- Check you have sufficient credits in your OpenAI account
- Test connection in Settings screen

**"Notifications not appearing"**
- Grant notification permissions when prompted
- Check device notification settings
- Ensure app is not in battery optimization mode (Android)

**"App crashes on startup"**
- Clear app data and restart
- Check Expo CLI version is up to date
- Verify all dependencies are installed

**"Analysis not working"**
- Check internet connection
- Verify OpenAI API key in Settings
- Try with shorter journal entries

### Debug Mode
Enable debug logging by setting `__DEV__` flag:
```javascript
// In src/lib/openai.ts
const DEBUG = __DEV__;
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for GPT-4 API
- Expo team for the amazing development platform
- React Native community for excellent libraries

---

**Made with ❤️ for personal growth and self-reflection**
