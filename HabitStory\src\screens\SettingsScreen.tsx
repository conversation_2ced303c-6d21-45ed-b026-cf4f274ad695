import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../context/AuthContext';
import { useOpenAI } from '../hooks/useOpenAI';
import '../lib/global.css';

const SettingsScreen: React.FC = () => {
  const [apiKey, setApiKey] = useState('');
  const [userName, setUserName] = useState('');
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  const { user, updateApiKey, updateUserName, logout } = useAuth();
  const { testConnection, checkApiKey } = useOpenAI();

  useEffect(() => {
    if (user) {
      setUserName(user.name);
    }
  }, [user]);

  const handleTestConnection = async () => {
    if (!apiKey.trim()) {
      Alert.alert('Missing API Key', 'Please enter your OpenAI API key first.');
      return;
    }

    setIsTestingConnection(true);
    
    try {
      // Temporarily update the API key for testing
      await updateApiKey(apiKey.trim());
      
      const isConnected = await testConnection();
      
      if (isConnected) {
        Alert.alert(
          'Connection Successful!',
          'Your OpenAI API key is working correctly.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Connection Failed',
          'Unable to connect to OpenAI. Please check your API key and internet connection.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      Alert.alert(
        'Connection Error',
        error instanceof Error ? error.message : 'Failed to test connection',
        [{ text: 'OK' }]
      );
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!userName.trim()) {
      Alert.alert('Missing Name', 'Please enter your name.');
      return;
    }

    if (!apiKey.trim()) {
      Alert.alert('Missing API Key', 'Please enter your OpenAI API key.');
      return;
    }

    setIsSaving(true);

    try {
      await Promise.all([
        updateUserName(userName.trim()),
        updateApiKey(apiKey.trim())
      ]);

      Alert.alert(
        'Settings Saved',
        'Your settings have been updated successfully.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Save Failed',
        error instanceof Error ? error.message : 'Failed to save settings',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSaving(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout? This will remove your API key from this device.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              Alert.alert('Error', 'Failed to logout');
            }
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1 px-4 py-2">
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Settings
          </Text>
          <Text className="text-gray-600">
            Configure your account and API settings
          </Text>
        </View>

        {/* User Information */}
        <View className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            User Information
          </Text>
          
          <Text className="text-gray-700 font-medium mb-2">Name</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 text-base mb-4"
            placeholder="Enter your name"
            value={userName}
            onChangeText={setUserName}
            autoCapitalize="words"
          />
        </View>

        {/* API Configuration */}
        <View className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            OpenAI API Configuration
          </Text>
          
          <Text className="text-gray-700 font-medium mb-2">API Key</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 text-base mb-3"
            placeholder="sk-..."
            value={apiKey}
            onChangeText={setApiKey}
            secureTextEntry={true}
            autoCapitalize="none"
            autoCorrect={false}
          />
          
          <Text className="text-gray-500 text-sm mb-4">
            Your API key is stored securely on your device and never shared.
          </Text>

          <TouchableOpacity
            className={`rounded-lg py-3 px-4 mb-3 ${
              isTestingConnection || !apiKey.trim()
                ? 'bg-gray-300'
                : 'bg-blue-500'
            }`}
            onPress={handleTestConnection}
            disabled={isTestingConnection || !apiKey.trim()}
          >
            {isTestingConnection ? (
              <View className="flex-row items-center justify-center">
                <ActivityIndicator color="white" size="small" />
                <Text className="text-white font-semibold ml-2">Testing...</Text>
              </View>
            ) : (
              <Text className="text-white font-semibold text-center">
                Test Connection
              </Text>
            )}
          </TouchableOpacity>
        </View>

        {/* API Key Help */}
        <View className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
          <Text className="text-blue-800 font-semibold mb-2">
            How to get your OpenAI API Key:
          </Text>
          <Text className="text-blue-700 text-sm leading-5">
            1. Visit platform.openai.com{'\n'}
            2. Sign in or create an account{'\n'}
            3. Go to API Keys section{'\n'}
            4. Create a new secret key{'\n'}
            5. Copy and paste it above
          </Text>
        </View>

        {/* Action Buttons */}
        <TouchableOpacity
          className={`rounded-lg py-4 px-6 mb-4 ${
            isSaving || !userName.trim() || !apiKey.trim()
              ? 'bg-gray-300'
              : 'bg-green-500'
          }`}
          onPress={handleSaveSettings}
          disabled={isSaving || !userName.trim() || !apiKey.trim()}
        >
          {isSaving ? (
            <View className="flex-row items-center justify-center">
              <ActivityIndicator color="white" size="small" />
              <Text className="text-white font-semibold ml-2">Saving...</Text>
            </View>
          ) : (
            <Text className="text-white font-semibold text-center text-lg">
              Save Settings
            </Text>
          )}
        </TouchableOpacity>

        {user && (
          <TouchableOpacity
            className="rounded-lg py-3 px-6 border border-red-300"
            onPress={handleLogout}
          >
            <Text className="text-red-600 font-semibold text-center">
              Logout
            </Text>
          </TouchableOpacity>
        )}

        <View className="h-8" />
      </ScrollView>
    </SafeAreaView>
  );
};

export default SettingsScreen;
