import { useState, useCallback } from 'react';
import { parseEntry, generateWeeklySummary, testApiConnection, hasApiKey } from '../lib/gemini';
import { getUserTraits, getLatestReport } from '../lib/db';

export interface UseGeminiReturn {
  isLoading: boolean;
  error: string | null;
  parseEntryText: (text: string) => Promise<any>;
  generateSummary: (weekEntries: any[]) => Promise<string>;
  testConnection: () => Promise<boolean>;
  checkApiKey: () => Promise<boolean>;
  clearError: () => void;
}

export const useGemini = (): UseGeminiReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const parseEntryText = useCallback(async (text: string) => {
    if (!text.trim()) {
      throw new Error('Entry text cannot be empty');
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get previous user traits for context
      const previousTraits = await getUserTraits();
      
      const result = await parseEntry(text, previousTraits);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to parse entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const generateSummary = useCallback(async (weekEntries: any[]) => {
    if (!weekEntries || weekEntries.length === 0) {
      throw new Error('No entries available for summary generation');
    }

    setIsLoading(true);
    setError(null);

    try {
      // Get user traits and previous feedback
      const userTraits = await getUserTraits();
      const latestReport = await getLatestReport();
      const previousFeedback = latestReport?.feedback_text || undefined;

      const result = await generateWeeklySummary(weekEntries, userTraits, previousFeedback);
      return result.html_content;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate summary';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const testConnection = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const isConnected = await testApiConnection();
      if (!isConnected) {
        setError('Failed to connect to Gemini API. Please check your API key and internet connection.');
      }
      return isConnected;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Connection test failed';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const checkApiKey = useCallback(async () => {
    try {
      return await hasApiKey();
    } catch (err) {
      setError('Failed to check API key status');
      return false;
    }
  }, []);

  return {
    isLoading,
    error,
    parseEntryText,
    generateSummary,
    testConnection,
    checkApiKey,
    clearError,
  };
};
