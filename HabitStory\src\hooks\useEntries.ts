import { useState, useEffect, useCallback } from 'react';
import { 
  insertEntry, 
  getEntry, 
  getAllEntries, 
  getWeekEntries, 
  updateEntry, 
  deleteEntry,
  formatDate,
  getWeekDateRange,
  Entry 
} from '../lib/db';

export interface UseEntriesReturn {
  entries: Entry[];
  currentEntry: Entry | null;
  isLoading: boolean;
  error: string | null;
  addEntry: (date: string, text: string, habits: string[], metrics: Record<string, any>, reflection: string) => Promise<void>;
  getEntryByDate: (date: string) => Promise<Entry | null>;
  getEntriesForWeek: (date: Date) => Promise<Entry[]>;
  editEntry: (id: number, text: string, habits: string[], metrics: Record<string, any>, reflection: string) => Promise<void>;
  removeEntry: (id: number) => Promise<void>;
  refreshEntries: () => Promise<void>;
  clearError: () => void;
}

export const useEntries = (): UseEntriesReturn => {
  const [entries, setEntries] = useState<Entry[]>([]);
  const [currentEntry, setCurrentEntry] = useState<Entry | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const refreshEntries = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const allEntries = await getAllEntries();
      setEntries(allEntries);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load entries';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const addEntry = useCallback(async (
    date: string,
    text: string,
    habits: string[],
    metrics: Record<string, any>,
    reflection: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      await insertEntry(date, text, habits, metrics, reflection);
      await refreshEntries();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [refreshEntries]);

  const getEntryByDate = useCallback(async (date: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const entry = await getEntry(date);
      setCurrentEntry(entry);
      return entry;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get entry';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getEntriesForWeek = useCallback(async (date: Date) => {
    setIsLoading(true);
    setError(null);

    try {
      const { start, end } = getWeekDateRange(date);
      const weekEntries = await getWeekEntries(start, end);
      return weekEntries;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get week entries';
      setError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  const editEntry = useCallback(async (
    id: number,
    text: string,
    habits: string[],
    metrics: Record<string, any>,
    reflection: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      await updateEntry(id, text, habits, metrics, reflection);
      await refreshEntries();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [refreshEntries]);

  const removeEntry = useCallback(async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      await deleteEntry(id);
      await refreshEntries();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete entry';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [refreshEntries]);

  // Load entries on mount
  useEffect(() => {
    refreshEntries();
  }, [refreshEntries]);

  return {
    entries,
    currentEntry,
    isLoading,
    error,
    addEntry,
    getEntryByDate,
    getEntriesForWeek,
    editEntry,
    removeEntry,
    refreshEntries,
    clearError,
  };
};
